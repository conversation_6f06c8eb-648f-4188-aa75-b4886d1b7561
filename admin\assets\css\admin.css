/* Admin Dashboard Styles */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 250px;
    height: 100vh;
    background-color: #212529;
    color: #fff;
    overflow-y: auto;
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #343a40;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    font-family: 'Playfair Display', serif;
}

/* Disable hover effects on the logo */
.sidebar-header h3:hover {
    color: #fff !important;
    text-decoration: none !important;
}

.sidebar-header p {
    margin: 5px 0 0;
    font-size: 0.9rem;
    opacity: 0.7;
}

.sidebar-menu {
    padding: 20px 0 40px 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 120px);
}

.sidebar-menu a {
    display: block;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s;
}

/* Logout button styling - same as other sidebar items */
.sidebar-menu a[href="logout.php"] {
    margin-top: 10px !important;
    border-top: 1px solid #343a40 !important;
    padding-top: 15px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 9999 !important;
}

.sidebar-menu a[href="logout.php"] i {
    margin-right: 10px !important;
    width: 20px !important;
    text-align: center !important;
}

.sidebar-menu a:hover, .sidebar-menu a.active {
    background-color: #343a40;
    color: #fff;
}

.sidebar-menu a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Content */
.content {
    margin-left: 250px;
    padding: 20px;
}

/* Stat Cards */
.stat-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.stat-card i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.stat-card h3 {
    margin: 10px 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.stat-card p {
    margin: 0;
    color: #6c757d;
}

/* Chart Container */
.chart-container {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .content {
        margin-left: 0;
    }

    .sidebar-menu a {
        padding: 10px;
    }
}

/* Navbar styles for admin header */
.navbar-brand {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    letter-spacing: 1px;
    color: #fff !important;
    text-decoration: none !important;
}

/* Disable hover effects on the logo */
.navbar-brand:hover {
    color: #fff !important;
    text-decoration: none !important;
}
