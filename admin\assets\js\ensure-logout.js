// Ensure logout button is visible on all admin pages
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Checking for logout button...');

    // Wait a bit for the page to fully load
    setTimeout(function() {
        const sidebarMenu = document.querySelector('.sidebar-menu');
        console.log('📍 Sidebar menu found:', sidebarMenu);

        if (!sidebarMenu) {
            console.log('❌ No sidebar menu found!');
            return;
        }

        // Always remove existing logout button first to avoid duplicates
        const existingLogout = sidebarMenu.querySelector('a[href="logout.php"]');
        if (existingLogout) {
            console.log('🗑️ Removing existing logout button');
            existingLogout.remove();
        }

        // Always add logout button manually to ensure it appears
        console.log('➕ Adding logout button manually...');
        const logoutLink = document.createElement('a');
        logoutLink.href = 'logout.php';
        logoutLink.innerHTML = '<i class="fas fa-sign-out-alt"></i> Logout';
        logoutLink.style.display = 'block !important';
        logoutLink.style.visibility = 'visible !important';
        logoutLink.style.opacity = '1 !important';
        logoutLink.style.padding = '12px 20px';
        logoutLink.style.color = 'rgba(255, 255, 255, 0.8)';
        logoutLink.style.textDecoration = 'none';
        logoutLink.style.transition = 'all 0.3s';
        logoutLink.style.marginTop = '10px';
        logoutLink.style.borderTop = '1px solid #343a40';
        logoutLink.style.paddingTop = '15px';
        logoutLink.style.position = 'relative';
        logoutLink.style.zIndex = '9999';

        // Add hover effect
        logoutLink.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#343a40';
            this.style.color = '#fff';
        });
        logoutLink.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'transparent';
            this.style.color = 'rgba(255, 255, 255, 0.8)';
        });

        sidebarMenu.appendChild(logoutLink);
        console.log('✅ Logout button added successfully!');

        // Double check it's visible
        setTimeout(function() {
            const addedButton = sidebarMenu.querySelector('a[href="logout.php"]');
            if (addedButton) {
                console.log('✅ Logout button confirmed visible:', addedButton);
            } else {
                console.log('❌ Logout button still not found after adding!');
            }
        }, 500);

    }, 200); // Wait 200ms for page to load
});
