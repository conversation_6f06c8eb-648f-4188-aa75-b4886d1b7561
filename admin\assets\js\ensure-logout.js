// Ensure logout button is visible on all admin pages
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Checking for logout button...');

    function ensureSidebarHeight() {
        const sidebar = document.querySelector('.sidebar');
        const sidebarMenu = document.querySelector('.sidebar-menu');

        if (sidebar && sidebarMenu) {
            // Force sidebar to have minimum height
            sidebar.style.minHeight = '100vh';
            sidebar.style.height = '100vh';
            sidebar.style.overflowY = 'auto';
            sidebar.style.display = 'flex';
            sidebar.style.flexDirection = 'column';

            // Force sidebar menu to take remaining space
            sidebarMenu.style.flex = '1';
            sidebarMenu.style.display = 'flex';
            sidebarMenu.style.flexDirection = 'column';
            sidebarMenu.style.minHeight = '400px';

            console.log('✅ Sidebar height ensured');
        }
    }

    function addLogoutButton() {
        const sidebarMenu = document.querySelector('.sidebar-menu');
        console.log('📍 Sidebar menu found:', sidebarMenu);

        if (!sidebarMenu) {
            console.log('❌ No sidebar menu found!');
            return;
        }

        // Ensure sidebar height first
        ensureSidebarHeight();

        // Remove any existing logout buttons to avoid duplicates
        const existingLogouts = sidebarMenu.querySelectorAll('a[href="logout.php"]');
        existingLogouts.forEach(logout => logout.remove());

        // Always add logout button manually to ensure it appears
        console.log('➕ Adding logout button manually...');
        const logoutLink = document.createElement('a');
        logoutLink.href = 'logout.php';
        logoutLink.innerHTML = '<i class="fas fa-sign-out-alt"></i> Logout';

        // Force all styles inline to override any CSS
        logoutLink.style.cssText = `
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            padding: 12px 20px !important;
            color: rgba(255, 255, 255, 0.8) !important;
            text-decoration: none !important;
            transition: all 0.3s !important;
            margin-top: auto !important;
            border-top: 1px solid #343a40 !important;
            padding-top: 15px !important;
            position: relative !important;
            z-index: 9999 !important;
            width: 100% !important;
            box-sizing: border-box !important;
        `;

        // Add hover effect
        logoutLink.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#343a40 !important';
            this.style.color = '#fff !important';
        });
        logoutLink.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'transparent !important';
            this.style.color = 'rgba(255, 255, 255, 0.8) !important';
        });

        sidebarMenu.appendChild(logoutLink);
        console.log('✅ Logout button added successfully!');

        // Double check it's visible and force scroll if needed
        setTimeout(function() {
            const finalButton = sidebarMenu.querySelector('a[href="logout.php"]');
            if (finalButton) {
                console.log('✅ Logout button confirmed visible:', finalButton);
                // Force visibility one more time
                finalButton.style.display = 'block !important';
                finalButton.style.visibility = 'visible !important';
                finalButton.style.opacity = '1 !important';

                // Ensure it's in view
                finalButton.scrollIntoView({ behavior: 'smooth', block: 'end' });
            } else {
                console.log('❌ Logout button still not found after adding!');
            }
        }, 100);
    }

    // Try multiple times to ensure it works
    setTimeout(addLogoutButton, 100);
    setTimeout(addLogoutButton, 500);
    setTimeout(addLogoutButton, 1000);

    // Also try when window loads
    window.addEventListener('load', addLogoutButton);

    // Try when window resizes
    window.addEventListener('resize', ensureSidebarHeight);
});
