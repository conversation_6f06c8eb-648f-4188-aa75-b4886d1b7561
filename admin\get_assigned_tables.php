<?php
session_start();
header('Content-Type: application/json');
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Get parameters
$reservation_id = isset($_GET['reservation_id']) ? (int)$_GET['reservation_id'] : 0;

// Validate parameters
if ($reservation_id <= 0) {
    echo json_encode([
        'error' => 'Invalid reservation ID.'
    ]);
    exit;
}

try {
    // Get assigned tables for the reservation
    $assigned_tables = getTablesForReservation($reservation_id);

    // Return successful response
    echo json_encode([
        'assigned_tables' => $assigned_tables,
        'reservation_id' => $reservation_id
    ]);

} catch (Exception $e) {
    echo json_encode([
        'error' => 'An error occurred while fetching assigned tables: ' . $e->getMessage()
    ]);
}
?>
