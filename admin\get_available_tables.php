<?php
session_start();
header('Content-Type: application/json');
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Get parameters
$reservation_id = isset($_GET['reservation_id']) ? (int)$_GET['reservation_id'] : null;
$date = isset($_GET['date']) ? sanitize($_GET['date']) : '';
$time = isset($_GET['time']) ? sanitize($_GET['time']) : '';
$guests = isset($_GET['guests']) ? (int)$_GET['guests'] : 0;

// Validate parameters
if (empty($date) || empty($time) || $guests <= 0) {
    echo json_encode([
        'error' => 'Invalid parameters. Date, time, and number of guests are required.'
    ]);
    exit;
}

// Validate date format
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
    echo json_encode([
        'error' => 'Invalid date format. Please use YYYY-MM-DD format.'
    ]);
    exit;
}

// Validate time format
if (!preg_match('/^\d{2}:\d{2}:\d{2}$/', $time)) {
    echo json_encode([
        'error' => 'Invalid time format. Please use HH:MM:SS format.'
    ]);
    exit;
}

try {
    // Get available tables (exclude current reservation if editing)
    $available_tables = getAvailableTables($date, $time, $guests, $reservation_id);

    // Return successful response
    echo json_encode([
        'available_tables' => $available_tables,
        'date' => $date,
        'time' => $time,
        'guests' => $guests,
        'reservation_id' => $reservation_id
    ]);

} catch (Exception $e) {
    echo json_encode([
        'error' => 'An error occurred while fetching available tables: ' . $e->getMessage()
    ]);
}
?>
