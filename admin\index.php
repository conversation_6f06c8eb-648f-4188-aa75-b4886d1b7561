<?php
ob_start();
session_start();
include '../config/config.php';
include '../includes/functions.php';

if (!isAdminLoggedIn()) {
    header("Location: login.php");
    exit();
}

$admin = getAdminById($_SESSION['admin_id']);

$stmt = $conn->prepare("SELECT SUM(total_price) as total_sales FROM orders WHERE status != 'cancelled'");
$stmt->execute();
$result = $stmt->get_result();
$total_sales = $result->fetch_assoc()['total_sales'] ?? 0;

$stmt = $conn->prepare("SELECT COUNT(*) as total_reservations FROM reservations WHERE status != 'cancelled'");
$stmt->execute();
$result = $stmt->get_result();
$total_reservations = $result->fetch_assoc()['total_reservations'] ?? 0;

$stmt = $conn->prepare("SELECT COUNT(*) as total_users FROM users");
$stmt->execute();
$result = $stmt->get_result();
$total_users = $result->fetch_assoc()['total_users'] ?? 0;

$stmt = $conn->prepare("SELECT COUNT(*) as available_tables FROM tables WHERE is_reserved = 0");
$stmt->execute();
$result = $stmt->get_result();
$available_tables = $result->fetch_assoc()['available_tables'] ?? 0;

$stmt = $conn->prepare("SELECT * FROM orders ORDER BY date DESC LIMIT 5");
$stmt->execute();
$result = $stmt->get_result();
$recent_orders = [];
while ($row = $result->fetch_assoc()) {
    $recent_orders[] = $row;
}

$stmt = $conn->prepare("SELECT r.*, u.name as user_name FROM reservations r JOIN users u ON r.user_id = u.id ORDER BY r.created_at DESC LIMIT 5");
$stmt->execute();
$result = $stmt->get_result();
$recent_reservations = [];
while ($row = $result->fetch_assoc()) {
    $recent_reservations[] = $row;
}

$stmt = $conn->prepare("
    SELECT
        DATE_FORMAT(date, '%Y-%m') as month,
        SUM(total_price) as monthly_sales
    FROM orders
    WHERE status != 'cancelled'
    GROUP BY DATE_FORMAT(date, '%Y-%m')
    ORDER BY month
    LIMIT 6
");
$stmt->execute();
$result = $stmt->get_result();
$monthly_sales = [];
$months = [];
$sales_data = [];

while ($row = $result->fetch_assoc()) {
    $month_name = date('M Y', strtotime($row['month'] . '-01'));
    $months[] = $month_name;
    $sales_data[] = $row['monthly_sales'];
}

$stmt = $conn->prepare("
    SELECT
        m.category,
        SUM(o.total_price) as category_sales
    FROM orders o
    JOIN menu_items m ON JSON_CONTAINS(o.items, JSON_OBJECT('id', m.id))
    WHERE o.status != 'cancelled'
    GROUP BY m.category
");
$stmt->execute();
$result = $stmt->get_result();
$category_sales = [];
$categories = [];
$category_data = [];

while ($row = $result->fetch_assoc()) {
    $categories[] = $row['category'];
    $category_data[] = $row['category_sales'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/admin/assets/css/admin.css?v=<?php echo time(); ?>">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        .recent-activity {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .activity-item {
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        /* Logout link styling - same as other sidebar items */
        .sidebar-menu a[href="logout.php"] {
            margin-top: 10px;
            border-top: 1px solid #343a40;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3 style="pointer-events: none;">RAYMA<span style="color: #cc1500;">R</span>T'S DINER</h3>
            <p>Admin Panel</p>
        </div>

        <div class="sidebar-menu">
            <a href="index.php" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="menu.php"><i class="fas fa-utensils"></i> Menu Management</a>
            <a href="orders.php"><i class="fas fa-shopping-cart"></i> Order Management</a>
            <a href="payments.php"><i class="fas fa-money-bill-wave"></i> Payment Management</a>
            <a href="reservations.php"><i class="fas fa-calendar-alt"></i> Reservations</a>
            <a href="tables.php"><i class="fas fa-chair"></i> Table Management</a>
            <a href="users.php"><i class="fas fa-users"></i> User Management</a>
            <a href="logout.php" style="margin-top: 10px !important; border-top: 1px solid #343a40 !important; padding-top: 15px !important; display: block !important; visibility: visible !important; opacity: 1 !important; position: relative !important; z-index: 99999 !important; color: rgba(255, 255, 255, 0.8) !important; text-decoration: none !important; padding: 12px 20px !important; width: 100% !important; box-sizing: border-box !important;"><i class="fas fa-sign-out-alt" style="margin-right: 10px !important;"></i> Logout</a>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Dashboard</h1>
                <div>
                    <span class="me-2">Welcome, <?php echo $admin['name']; ?></span>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-money-bill-wave text-success"></i>
                        <h3><?php echo formatCurrency($total_sales); ?></h3>
                        <p>Total Sales</p>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-calendar-check text-primary"></i>
                        <h3><?php echo $total_reservations; ?></h3>
                        <p>Total Reservations</p>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-users text-info"></i>
                        <h3><?php echo $total_users; ?></h3>
                        <p>Registered Users</p>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-chair text-warning"></i>
                        <h3><?php echo $available_tables; ?></h3>
                        <p>Available Tables</p>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4>Monthly Sales</h4>
                        <canvas id="monthlySalesChart"></canvas>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="chart-container">
                        <h4>Sales by Category</h4>
                        <canvas id="categorySalesChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-md-6">
                    <div class="recent-activity">
                        <h4>Recent Orders</h4>

                        <?php if (empty($recent_orders)): ?>
                            <p class="text-muted">No recent orders.</p>
                        <?php else: ?>
                            <?php foreach ($recent_orders as $order): ?>
                                <div class="activity-item">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>Order #<?php echo $order['id']; ?></strong>
                                            <p class="text-muted mb-0">
                                                <?php
                                                $user = getUserById($order['user_id']);
                                                echo $user ? $user['name'] : 'Guest';
                                                ?>
                                            </p>
                                        </div>
                                        <div>
                                            <span class="badge bg-<?php echo $order['status'] == 'completed' ? 'success' : ($order['status'] == 'cancelled' ? 'danger' : 'warning'); ?>">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                            <p class="text-muted mb-0"><?php echo formatCurrency($order['total_price']); ?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                            <div class="text-center mt-3">
                                <a href="orders.php" class="btn btn-outline-primary btn-sm">View All Orders</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="recent-activity">
                        <h4>Recent Reservations</h4>

                        <?php if (empty($recent_reservations)): ?>
                            <p class="text-muted">No recent reservations.</p>
                        <?php else: ?>
                            <?php foreach ($recent_reservations as $reservation): ?>
                                <div class="activity-item">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong><?php echo $reservation['user_name']; ?></strong>
                                            <p class="text-muted mb-0">
                                                <?php echo formatDate($reservation['date']); ?> at <?php echo formatTime($reservation['time']); ?>
                                            </p>
                                        </div>
                                        <div>
                                            <span class="badge bg-<?php echo $reservation['status'] == 'confirmed' ? 'success' : ($reservation['status'] == 'cancelled' ? 'danger' : 'warning'); ?>">
                                                <?php echo ucfirst($reservation['status']); ?>
                                            </span>
                                            <p class="text-muted mb-0"><?php echo $reservation['guests']; ?> guests</p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                            <div class="text-center mt-3">
                                <a href="reservations.php" class="btn btn-outline-primary btn-sm">View All Reservations</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Ensure logout button is visible -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/ensure-logout.js?v=<?php echo time(); ?>"></script>

    <!-- Force logout button to be visible -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const logoutButton = document.querySelector('a[href="logout.php"]');
                if (logoutButton) {
                    console.log('Found logout button, forcing visibility');
                    logoutButton.style.cssText = `
                        margin-top: 10px !important;
                        border-top: 1px solid #343a40 !important;
                        padding-top: 15px !important;
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        position: relative !important;
                        z-index: 99999 !important;
                        color: rgba(255, 255, 255, 0.8) !important;
                        text-decoration: none !important;
                        padding: 12px 20px !important;
                        width: 100% !important;
                        box-sizing: border-box !important;
                        background-color: transparent !important;
                    `;
                } else {
                    console.log('Logout button not found, creating one');
                    const sidebarMenu = document.querySelector('.sidebar-menu');
                    if (sidebarMenu) {
                        const logoutLink = document.createElement('a');
                        logoutLink.href = 'logout.php';
                        logoutLink.innerHTML = '<i class="fas fa-sign-out-alt" style="margin-right: 10px !important;"></i> Logout';
                        logoutLink.style.cssText = `
                            margin-top: 10px !important;
                            border-top: 1px solid #343a40 !important;
                            padding-top: 15px !important;
                            display: block !important;
                            visibility: visible !important;
                            opacity: 1 !important;
                            position: relative !important;
                            z-index: 99999 !important;
                            color: rgba(255, 255, 255, 0.8) !important;
                            text-decoration: none !important;
                            padding: 12px 20px !important;
                            width: 100% !important;
                            box-sizing: border-box !important;
                            background-color: transparent !important;
                        `;
                        sidebarMenu.appendChild(logoutLink);
                    }
                }
            }, 100);
        });
    </script>

    <script>
        var monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');
        var monthlySalesChart = new Chart(monthlySalesCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($months); ?>,
                datasets: [{
                    label: 'Monthly Sales',
                    data: <?php echo json_encode($sales_data); ?>,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    tension: 0.1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value, index, values) {
                                return '₱' + value.toFixed(2);
                            }
                        }
                    }
                }
            }
        });

        var categorySalesCtx = document.getElementById('categorySalesChart').getContext('2d');
        var categorySalesChart = new Chart(categorySalesCtx, {
            type: 'pie',
            data: {
                labels: <?php echo json_encode($categories); ?>,
                datasets: [{
                    data: <?php echo json_encode($category_data); ?>,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.raw || 0;
                                return label + ': ₱' + value.toFixed(2);
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
<?php
ob_end_flush();
?>
