<?php
session_start();
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header("Location: login.php");
    exit();
}

// Handle menu item actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Add new menu item
    if (isset($_POST['add_item'])) {
        $name = sanitize($_POST['name']);
        $category = sanitize($_POST['category']);
        $price = (float)$_POST['price'];
        $description = sanitize($_POST['description']);
        $image = '';

        // Validate form data
        $errors = [];

        if (empty($name)) {
            $errors[] = 'Name is required!';
        }

        if (empty($category)) {
            $errors[] = 'Category is required!';
        }

        if (empty($price) || $price <= 0) {
            $errors[] = 'Price must be greater than 0!';
        }

        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $max_size = 2 * 1024 * 1024; // 2MB

            if (!in_array($_FILES['image']['type'], $allowed_types)) {
                $errors[] = 'Only JPG, PNG, and GIF images are allowed!';
            } else if ($_FILES['image']['size'] > $max_size) {
                $errors[] = 'Image size must be less than 2MB!';
            } else {
                // Generate unique filename
                $image = uniqid() . '_' . $_FILES['image']['name'];
                $upload_path = '../uploads/' . $image;

                // Move uploaded file
                if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    $errors[] = 'Failed to upload image!';
                    $image = '';
                }
            }
        }

        // If no errors, add menu item
        if (empty($errors)) {
            // Get category ID for normalized structure
            $category_id = null;
            if (!empty($category)) {
                // Check if category exists, if not create it
                $category_data = getCategoryByName($category);
                if ($category_data) {
                    $category_id = $category_data['id'];
                } else {
                    // Create new category
                    $stmt = $conn->prepare("INSERT INTO categories (name, display_order) VALUES (?, (SELECT COALESCE(MAX(display_order), 0) + 1 FROM categories c))");
                    $stmt->bind_param("s", $category);
                    if ($stmt->execute()) {
                        $category_id = $stmt->insert_id;
                    }
                }
            }

            // Insert with both old and new fields for backward compatibility
            $stmt = $conn->prepare("INSERT INTO menu_items (name, category, category_id, price, image, description) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("ssidss", $name, $category, $category_id, $price, $image, $description);

            if ($stmt->execute()) {
                // Set success message
                $_SESSION['message'] = 'Menu item added successfully!';
                $_SESSION['message_type'] = 'success';
            } else {
                // Set error message
                $_SESSION['message'] = 'Failed to add menu item. Please try again.';
                $_SESSION['message_type'] = 'danger';
            }
        } else {
            // Set error message
            $_SESSION['message'] = implode('<br>', $errors);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Update menu item
    else if (isset($_POST['update_item'])) {
        $id = (int)$_POST['id'];
        $name = sanitize($_POST['name']);
        $category = sanitize($_POST['category']);
        $price = (float)$_POST['price'];
        $description = sanitize($_POST['description']);

        // Validate form data
        $errors = [];

        if (empty($name)) {
            $errors[] = 'Name is required!';
        }

        if (empty($category)) {
            $errors[] = 'Category is required!';
        }

        if (empty($price) || $price <= 0) {
            $errors[] = 'Price must be greater than 0!';
        }

        // Get current menu item
        $stmt = $conn->prepare("SELECT image FROM menu_items WHERE id = ?");
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $current_item = $result->fetch_assoc();

        $image = $current_item['image'];

        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $max_size = 2 * 1024 * 1024; // 2MB

            if (!in_array($_FILES['image']['type'], $allowed_types)) {
                $errors[] = 'Only JPG, PNG, and GIF images are allowed!';
            } else if ($_FILES['image']['size'] > $max_size) {
                $errors[] = 'Image size must be less than 2MB!';
            } else {
                // Delete old image if exists
                if (!empty($current_item['image'])) {
                    $old_image_path = '../uploads/' . $current_item['image'];
                    if (file_exists($old_image_path)) {
                        unlink($old_image_path);
                    }
                }

                // Generate unique filename
                $image = uniqid() . '_' . $_FILES['image']['name'];
                $upload_path = '../uploads/' . $image;

                // Move uploaded file
                if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    $errors[] = 'Failed to upload image!';
                    $image = $current_item['image'];
                }
            }
        }

        // If no errors, update menu item
        if (empty($errors)) {
            // Get category ID for normalized structure
            $category_id = null;
            if (!empty($category)) {
                // Check if category exists, if not create it
                $category_data = getCategoryByName($category);
                if ($category_data) {
                    $category_id = $category_data['id'];
                } else {
                    // Create new category
                    $stmt = $conn->prepare("INSERT INTO categories (name, display_order) VALUES (?, (SELECT COALESCE(MAX(display_order), 0) + 1 FROM categories c))");
                    $stmt->bind_param("s", $category);
                    if ($stmt->execute()) {
                        $category_id = $stmt->insert_id;
                    }
                }
            }

            // Update with both old and new fields for backward compatibility
            $stmt = $conn->prepare("UPDATE menu_items SET name = ?, category = ?, category_id = ?, price = ?, image = ?, description = ? WHERE id = ?");
            $stmt->bind_param("ssidssi", $name, $category, $category_id, $price, $image, $description, $id);

            if ($stmt->execute()) {
                // Set success message
                $_SESSION['message'] = 'Menu item updated successfully!';
                $_SESSION['message_type'] = 'success';
            } else {
                // Set error message
                $_SESSION['message'] = 'Failed to update menu item. Please try again.';
                $_SESSION['message_type'] = 'danger';
            }
        } else {
            // Set error message
            $_SESSION['message'] = implode('<br>', $errors);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Delete menu item
    else if (isset($_POST['delete_item'])) {
        $id = (int)$_POST['id'];

        // Get menu item image
        $stmt = $conn->prepare("SELECT image FROM menu_items WHERE id = ?");
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $item = $result->fetch_assoc();

        // Delete menu item
        $stmt = $conn->prepare("DELETE FROM menu_items WHERE id = ?");
        $stmt->bind_param("i", $id);

        if ($stmt->execute()) {
            // Delete image if exists
            if (!empty($item['image'])) {
                $image_path = '../uploads/' . $item['image'];
                if (file_exists($image_path)) {
                    unlink($image_path);
                }
            }

            // Set success message
            $_SESSION['message'] = 'Menu item deleted successfully!';
            $_SESSION['message_type'] = 'success';
        } else {
            // Set error message
            $_SESSION['message'] = 'Failed to delete menu item. Please try again.';
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Redirect to refresh page
    header("Location: menu.php");
    exit();
}

// Get all menu items with category information
$stmt = $conn->prepare("
    SELECT mi.*, c.name as category_name, c.display_order
    FROM menu_items mi
    LEFT JOIN categories c ON mi.category_id = c.id
    ORDER BY c.display_order, c.name, mi.name
");
$stmt->execute();
$result = $stmt->get_result();
$menu_items = [];
while ($row = $result->fetch_assoc()) {
    // Ensure backward compatibility
    if (!isset($row['category']) && isset($row['category_name'])) {
        $row['category'] = $row['category_name'];
    }
    $menu_items[] = $row;
}

// Get all categories
$categories = getMenuCategories();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu Management - <?php echo SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Admin CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/admin.css">

    <style>
        .menu-item-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 5px;
        }

        /* Style for Edit button */
        .btn-dark {
            background-color: #212529;
            border-color: #212529;
            box-shadow: none;
        }

        .btn-dark:hover,
        .btn-dark:focus,
        .btn-dark:active {
            background-color: #1a1e21;
            border-color: #1a1e21;
            box-shadow: none !important;
            transform: none !important;
        }

        /* Logout link styling - same as other sidebar items */
        .sidebar-menu a[href="logout.php"] {
            margin-top: 10px;
            border-top: 1px solid #343a40;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>RAYMA<span style="color: #cc1500;">R</span>T'S DINER</h3>
            <p>Admin Panel</p>
        </div>

        <div class="sidebar-menu">
            <a href="index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="menu.php" class="active"><i class="fas fa-utensils"></i> Menu Management</a>
            <a href="orders.php"><i class="fas fa-shopping-cart"></i> Order Management</a>
            <a href="payments.php"><i class="fas fa-money-bill-wave"></i> Payment Management</a>
            <a href="reservations.php"><i class="fas fa-calendar-alt"></i> Reservations</a>
            <a href="tables.php"><i class="fas fa-chair"></i> Table Management</a>
            <a href="users.php"><i class="fas fa-users"></i> User Management</a>
            <a href="logout.php" style="margin-top: 10px; border-top: 1px solid #343a40; padding-top: 15px;"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Menu Management</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                    <i class="fas fa-plus"></i> Add New Item
                </button>
            </div>

            <?php if (isset($_SESSION['message'])): ?>
                <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
            <?php endif; ?>

            <!-- Menu Items Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Description</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($menu_items)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center">No menu items found.</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($menu_items as $item): ?>
                                        <tr>
                                            <td>
                                                <img src="<?php echo !empty($item['image']) ? '../uploads/' . $item['image'] : '../assets/images/default-food.jpg'; ?>" alt="<?php echo $item['name']; ?>" class="menu-item-image">
                                            </td>
                                            <td><?php echo $item['name']; ?></td>
                                            <td><?php echo $item['category']; ?></td>
                                            <td><?php echo formatCurrency($item['price']); ?></td>
                                            <td><?php echo substr($item['description'], 0, 50) . (strlen($item['description']) > 50 ? '...' : ''); ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-dark edit-item-btn" data-bs-toggle="modal" data-bs-target="#editItemModal" data-id="<?php echo $item['id']; ?>" data-name="<?php echo $item['name']; ?>" data-category="<?php echo $item['category']; ?>" data-price="<?php echo $item['price']; ?>" data-description="<?php echo $item['description']; ?>" data-image="<?php echo $item['image']; ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger delete-item-btn" data-bs-toggle="modal" data-bs-target="#deleteItemModal" data-id="<?php echo $item['id']; ?>" data-name="<?php echo $item['name']; ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addItemModalLabel">Add New Menu Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="menu.php" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="category" name="category" list="categories" required>
                            <datalist id="categories">
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category; ?>">
                                <?php endforeach; ?>
                            </datalist>
                            <small class="text-muted">You can select an existing category or create a new one</small>
                        </div>

                        <div class="mb-3">
                            <label for="price" class="form-label">Price</label>
                            <div class="input-group">
                                <span class="input-group-text">₱</span>
                                <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            <small class="text-muted">Max file size: 2MB. Allowed formats: JPG, PNG, GIF</small>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" name="add_item">Add Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Item Modal -->
    <div class="modal fade" id="editItemModal" tabindex="-1" aria-labelledby="editItemModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editItemModalLabel">Edit Menu Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="menu.php" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" id="edit_id" name="id">

                        <div class="mb-3">
                            <label for="edit_name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="edit_category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="edit_category" name="category" list="categories" required>
                        </div>

                        <div class="mb-3">
                            <label for="edit_price" class="form-label">Price</label>
                            <div class="input-group">
                                <span class="input-group-text">₱</span>
                                <input type="number" class="form-control" id="edit_price" name="price" step="0.01" min="0" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_image" class="form-label">Image</label>
                            <input type="file" class="form-control" id="edit_image" name="image" accept="image/*">
                            <small class="text-muted">Leave empty to keep current image</small>
                            <div id="current_image_container" class="mt-2"></div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-dark" name="update_item">Update Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Item Modal -->
    <div class="modal fade" id="deleteItemModal" tabindex="-1" aria-labelledby="deleteItemModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteItemModalLabel">Delete Menu Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete <span id="delete_item_name"></span>?</p>
                    <p class="text-danger">This action cannot be undone!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="post" action="menu.php">
                        <input type="hidden" id="delete_id" name="id">
                        <button type="submit" class="btn btn-danger" name="delete_item">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Ensure logout button is visible -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/ensure-logout.js?v=<?php echo time(); ?>"></script>

    <script>
        // Edit Item Modal
        document.querySelectorAll('.edit-item-btn').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const category = this.getAttribute('data-category');
                const price = this.getAttribute('data-price');
                const description = this.getAttribute('data-description');
                const image = this.getAttribute('data-image');

                document.getElementById('edit_id').value = id;
                document.getElementById('edit_name').value = name;
                document.getElementById('edit_category').value = category;
                document.getElementById('edit_price').value = price;
                document.getElementById('edit_description').value = description;

                const currentImageContainer = document.getElementById('current_image_container');
                currentImageContainer.innerHTML = '';

                if (image) {
                    const img = document.createElement('img');
                    img.src = '../uploads/' + image;
                    img.alt = name;
                    img.className = 'img-thumbnail';
                    img.style.maxWidth = '100px';
                    currentImageContainer.appendChild(img);
                } else {
                    currentImageContainer.innerHTML = '<p class="text-muted">No image</p>';
                }
            });
        });

        // Delete Item Modal
        document.querySelectorAll('.delete-item-btn').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');

                document.getElementById('delete_id').value = id;
                document.getElementById('delete_item_name').textContent = name;
            });
        });
    </script>
</body>
</html>
