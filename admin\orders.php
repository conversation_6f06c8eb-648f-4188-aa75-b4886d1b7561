<?php
session_start();
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header("Location: login.php");
    exit();
}

// Handle order actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Update order status
    if (isset($_POST['update_status'])) {
        $order_id = (int)$_POST['order_id'];
        $status = sanitize($_POST['status']);
        $payment_method = sanitize($_POST['payment_method']);

        $stmt = $conn->prepare("UPDATE orders SET status = ?, payment_method = ? WHERE id = ?");
        $stmt->bind_param("ssi", $status, $payment_method, $order_id);

        if ($stmt->execute()) {
            // Get order details
            $stmt = $conn->prepare("SELECT * FROM orders WHERE id = ?");
            $stmt->bind_param("i", $order_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $order = $result->fetch_assoc();

            // Get user details
            $user = getUserById($order['user_id']);

            // Check if a payment transaction exists for this order
            $payment_exists = false;
            $stmt = $conn->prepare("SELECT id FROM payment_transactions WHERE order_id = ?");
            $stmt->bind_param("i", $order_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $payment_exists = true;
                $payment_transaction = $result->fetch_assoc();

                // Update the payment transaction status if order is completed or cancelled
                if ($status == 'completed') {
                    $transaction_status = 'completed';
                    $stmt = $conn->prepare("UPDATE payment_transactions SET transaction_status = ? WHERE order_id = ?");
                    $stmt->bind_param("si", $transaction_status, $order_id);
                    $stmt->execute();
                } else if ($status == 'cancelled') {
                    $transaction_status = 'failed';
                    $stmt = $conn->prepare("UPDATE payment_transactions SET transaction_status = ? WHERE order_id = ?");
                    $stmt->bind_param("si", $transaction_status, $order_id);
                    $stmt->execute();
                }
            } else {
                // Create a new payment transaction
                $transaction_data = [
                    'order_id' => $order_id,
                    'amount' => $order['total_price'],
                    'payment_method' => $payment_method,
                    'transaction_status' => ($status == 'completed') ? 'completed' : 'pending',
                    'transaction_reference' => 'ORD-' . $order_id . '-' . date('YmdHis'),
                    'notes' => 'Created from order management'
                ];

                createPaymentTransaction($transaction_data);
            }

            // Send email notification
            if ($user) {
                $subject = 'Order Status Update - ' . SITE_NAME;
                $message = "Dear " . $user['name'] . ",\n\n";
                $message .= "The status of your order #" . $order_id . " has been updated to " . ucfirst($status) . ".\n\n";
                $message .= "Thank you for choosing " . SITE_NAME . "!\n";

                sendEmail($user['email'], $subject, $message, $user['id'], 'order');
            }

            // Set success message
            $_SESSION['message'] = 'Order status updated successfully!';
            $_SESSION['message_type'] = 'success';
        } else {
            // Set error message
            $_SESSION['message'] = 'Failed to update order status. Please try again.';
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Redirect to refresh page
    header("Location: orders.php");
    exit();
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$date_filter = isset($_GET['date']) ? sanitize($_GET['date']) : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query
$query = "SELECT o.*, u.name as user_name, u.email as user_email FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE 1=1";
$params = [];
$types = "";

if (!empty($status_filter)) {
    $query .= " AND o.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

if (!empty($date_filter)) {
    $query .= " AND DATE(o.date) = ?";
    $params[] = $date_filter;
    $types .= "s";
}

if (!empty($search)) {
    $search_term = "%$search%";
    $query .= " AND (o.id LIKE ? OR u.name LIKE ? OR u.email LIKE ?)";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $types .= "sss";
}

$query .= " ORDER BY o.date DESC";

// Prepare and execute query
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

$orders = [];
while ($row = $result->fetch_assoc()) {
    $orders[] = $row;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Management - <?php echo SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Admin CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/admin.css">

    <!-- Custom CSS for Order Buttons -->
    <style>
        .action-buttons .btn {
            min-width: 100%;
            text-align: center;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            border-radius: 4px;
            font-weight: normal;
            box-shadow: none;
        }

        .action-buttons .btn i {
            margin-right: 5px;
        }

        /* Ensure all buttons have consistent styling */
        .action-buttons .btn-dark,
        .action-buttons .btn-success,
        .action-buttons .btn-info {
            box-shadow: none;
        }

        .action-buttons .btn:hover,
        .action-buttons .btn:focus,
        .action-buttons .btn:active {
            box-shadow: none !important;
        }

        @media (max-width: 992px) {
            .action-buttons {
                display: flex;
                flex-direction: column;
                gap: 5px;
            }
        }

        /* Logout link styling - same as other sidebar items */
        .sidebar-menu a[href="logout.php"] {
            margin-top: 10px;
            border-top: 1px solid #343a40;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>RAYMA<span style="color: #cc1500;">R</span>T'S DINER</h3>
            <p>Admin Panel</p>
        </div>

        <div class="sidebar-menu">
            <a href="index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="menu.php"><i class="fas fa-utensils"></i> Menu Management</a>
            <a href="orders.php" class="active"><i class="fas fa-shopping-cart"></i> Order Management</a>
            <a href="payments.php"><i class="fas fa-money-bill-wave"></i> Payment Management</a>
            <a href="reservations.php"><i class="fas fa-calendar-alt"></i> Reservations</a>
            <a href="tables.php"><i class="fas fa-chair"></i> Table Management</a>
            <a href="users.php"><i class="fas fa-users"></i> User Management</a>
            <a href="logout.php" style="margin-top: 10px; border-top: 1px solid #343a40; padding-top: 15px;"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="container-fluid">
            <h1 class="mb-4">Order Management</h1>

            <?php if (isset($_SESSION['message'])): ?>
                <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
            <?php endif; ?>

            <!-- Filters -->
            <div class="filter-card">
                <form method="get" action="orders.php" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="preparing" <?php echo $status_filter == 'preparing' ? 'selected' : ''; ?>>Preparing</option>
                            <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="cancelled" <?php echo $status_filter == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="date" class="form-label">Date</label>
                        <input type="date" class="form-control" id="date" name="date" value="<?php echo $date_filter; ?>">
                    </div>

                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Order ID, Customer Name, Email" value="<?php echo $search; ?>">
                    </div>

                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">Filter</button>
                        <a href="orders.php" class="btn btn-secondary">Reset</a>
                    </div>
                </form>
            </div>

            <!-- Orders Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 5%;">Order ID</th>
                                    <th style="width: 15%;">Customer</th>
                                    <th style="width: 10%;">Date</th>
                                    <th style="width: 10%;">Items</th>
                                    <th style="width: 8%;">Total</th>
                                    <th style="width: 10%;">Payment Method</th>
                                    <th style="width: 7%;">Status</th>
                                    <th style="width: 35%;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($orders)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center">No orders found.</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($orders as $order): ?>
                                        <tr>
                                            <td>#<?php echo $order['id']; ?></td>
                                            <td>
                                                <?php if ($order['user_id']): ?>
                                                    <?php echo $order['user_name']; ?><br>
                                                    <small class="text-muted"><?php echo $order['user_email']; ?></small>
                                                <?php else: ?>
                                                    Guest
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo formatDate($order['date']) . '<br><small class="text-muted">' . date('g:i A', strtotime($order['date'])) . '</small>'; ?></td>
                                            <td>
                                                <?php
                                                // Get order items from the normalized table
                                                $stmt_items = $conn->prepare("SELECT COUNT(*) as item_count FROM order_items WHERE order_id = ?");
                                                $stmt_items->bind_param("i", $order['id']);
                                                $stmt_items->execute();
                                                $result_items = $stmt_items->get_result();
                                                $item_count_row = $result_items->fetch_assoc();

                                                // If no items found in the normalized table, fall back to the JSON data
                                                if ($item_count_row['item_count'] > 0) {
                                                    $item_count = $item_count_row['item_count'];
                                                } else {
                                                    $items = json_decode($order['items'], true);
                                                    $item_count = count($items);
                                                }

                                                echo $item_count . ' item' . ($item_count > 1 ? 's' : '');
                                                ?>
                                                <div class="text-center">
                                                    <a href="#" class="btn btn-sm btn-link p-0" data-bs-toggle="modal" data-bs-target="#viewOrderModal" data-order='<?php echo htmlspecialchars(json_encode($order), ENT_QUOTES, 'UTF-8'); ?>'>
                                                        View Details
                                                    </a>
                                                </div>
                                            </td>
                                            <td><?php echo formatCurrency($order['total_price']); ?></td>
                                            <td><?php echo ucwords(str_replace('_', ' ', $order['payment_method'] ?? 'cash')); ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                switch ($order['status']) {
                                                    case 'pending':
                                                        $status_class = 'bg-warning';
                                                        break;
                                                    case 'preparing':
                                                        $status_class = 'bg-info';
                                                        break;
                                                    case 'completed':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'cancelled':
                                                        $status_class = 'bg-danger';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>"><?php echo ucfirst($order['status']); ?></span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button type="button" class="btn btn-sm btn-dark" data-bs-toggle="modal" data-bs-target="#updateStatusModal" data-order-id="<?php echo $order['id']; ?>" data-status="<?php echo $order['status']; ?>">
                                                        <i class="fas fa-edit"></i> Update Status
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#printReceiptModal" data-order='<?php echo htmlspecialchars(json_encode($order), ENT_QUOTES, 'UTF-8'); ?>'>
                                                        <i class="fas fa-print"></i> Print Receipt
                                                    </button>
                                                    <a href="payments.php?order_id=<?php echo $order['id']; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-money-bill-wave"></i> Manage Payment
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- View Order Modal -->
    <div class="modal fade" id="viewOrderModal" tabindex="-1" aria-labelledby="viewOrderModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewOrderModalLabel">Order Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Order Information</h6>
                            <p><strong>Order ID:</strong> #<span id="order_id"></span></p>
                            <p><strong>Date:</strong> <span id="order_date"></span></p>
                            <p><strong>Payment Method:</strong> <span id="order_payment_method"></span></p>
                            <p><strong>Status:</strong> <span id="order_status"></span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Customer Information</h6>
                            <p><strong>Name:</strong> <span id="customer_name"></span></p>
                            <p><strong>Email:</strong> <span id="customer_email"></span></p>
                        </div>
                    </div>

                    <h6>Order Items</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Subtotal</th>
                                </tr>
                            </thead>
                            <tbody id="order_items">
                                <!-- Order items will be populated here -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3" class="text-end">Total:</th>
                                    <th id="order_total"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateStatusModalLabel">Update Order Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="orders.php">
                    <div class="modal-body">
                        <input type="hidden" id="update_order_id" name="order_id">

                        <div class="mb-3">
                            <label for="payment_method" class="form-label">Payment Method</label>
                            <select class="form-select" id="update_payment_method" name="payment_method" required>
                                <option value="cash">Cash</option>
                                <option value="credit_card">Credit Card</option>
                                <option value="debit_card">Debit Card</option>
                                <option value="gcash">GCash</option>
                                <option value="paymaya">PayMaya</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="update_status" name="status" required>
                                <option value="pending">Pending</option>
                                <option value="preparing">Preparing</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" name="update_status">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Print Receipt Modal -->
    <div class="modal fade" id="printReceiptModal" tabindex="-1" aria-labelledby="printReceiptModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="printReceiptModalLabel">Print Receipt</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="receipt" class="p-4">
                        <div class="text-center mb-4">
                            <h3>RAYMA<span style="color: #cc1500;">R</span>T'S DINER</h3>
                            <p>Purok 6A, Tagbuyacan, Santiago, Agusan del Norte</p>
                            <p>Phone: +63 ************ | Email: <EMAIL></p>
                            <hr>
                            <h4>RECEIPT</h4>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <p><strong>Order ID:</strong> #<span id="receipt_order_id"></span></p>
                                <p><strong>Date:</strong> <span id="receipt_date"></span></p>
                                <p><strong>Payment Method:</strong> <span id="receipt_payment_method"></span></p>
                            </div>
                            <div class="col-6 text-end">
                                <p><strong>Customer:</strong> <span id="receipt_customer"></span></p>
                                <p><strong>Email:</strong> <span id="receipt_email"></span></p>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Subtotal</th>
                                    </tr>
                                </thead>
                                <tbody id="receipt_items">
                                    <!-- Receipt items will be populated here -->
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="3" class="text-end">Total:</th>
                                        <th id="receipt_total"></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <div class="text-center mt-4">
                            <p>Thank you for dining with us!</p>
                            <p>Please come again.</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="printReceipt()">
                        <i class="fas fa-print"></i> Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>

    <!-- Ensure logout button is visible -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/ensure-logout.js?v=<?php echo time(); ?>"></script>

    <script>
        // View Order Modal
        const viewOrderModal = document.getElementById('viewOrderModal');
        viewOrderModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const orderData = JSON.parse(button.getAttribute('data-order'));

            document.getElementById('order_id').textContent = orderData.id;
            document.getElementById('order_date').textContent = new Date(orderData.date).toLocaleString();

            // Format payment method (convert snake_case to Title Case)
            const paymentMethod = orderData.payment_method || 'cash';
            document.getElementById('order_payment_method').textContent = paymentMethod.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            let statusClass = '';
            switch (orderData.status) {
                case 'pending':
                    statusClass = 'bg-warning';
                    break;
                case 'preparing':
                    statusClass = 'bg-info';
                    break;
                case 'completed':
                    statusClass = 'bg-success';
                    break;
                case 'cancelled':
                    statusClass = 'bg-danger';
                    break;
            }

            document.getElementById('order_status').innerHTML = `<span class="badge ${statusClass}">${orderData.status.charAt(0).toUpperCase() + orderData.status.slice(1)}</span>`;
            document.getElementById('customer_name').textContent = orderData.user_name || 'Guest';
            document.getElementById('customer_email').textContent = orderData.user_email || 'N/A';

            const orderItemsContainer = document.getElementById('order_items');
            orderItemsContainer.innerHTML = '';

            // Fetch order items from the normalized table using AJAX
            fetch(`get_order_items.php?order_id=${orderData.id}`)
                .then(response => response.json())
                .then(items => {
                    if (items.length > 0) {
                        // Use normalized data
                        items.forEach(item => {
                            const subtotal = item.price * item.quantity;
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${item.name}</td>
                                <td>₱${parseFloat(item.price).toFixed(2)}</td>
                                <td>${item.quantity}</td>
                                <td>₱${subtotal.toFixed(2)}</td>
                            `;
                            orderItemsContainer.appendChild(row);
                        });
                    } else {
                        // Fall back to JSON data if no normalized data is available
                        const orderItems = JSON.parse(orderData.items);
                        orderItems.forEach(item => {
                            const subtotal = item.price * item.quantity;
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${item.name}</td>
                                <td>₱${parseFloat(item.price).toFixed(2)}</td>
                                <td>${item.quantity}</td>
                                <td>₱${subtotal.toFixed(2)}</td>
                            `;
                            orderItemsContainer.appendChild(row);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching order items:', error);
                    // Fall back to JSON data if there's an error
                    const orderItems = JSON.parse(orderData.items);
                    orderItems.forEach(item => {
                        const subtotal = item.price * item.quantity;
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${item.name}</td>
                            <td>₱${parseFloat(item.price).toFixed(2)}</td>
                            <td>${item.quantity}</td>
                            <td>₱${subtotal.toFixed(2)}</td>
                        `;
                        orderItemsContainer.appendChild(row);
                    });
                });

            document.getElementById('order_total').textContent = `₱${parseFloat(orderData.total_price).toFixed(2)}`;
        });

        // Update Status Modal
        const updateStatusModal = document.getElementById('updateStatusModal');
        updateStatusModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const orderId = button.getAttribute('data-order-id');
            const status = button.getAttribute('data-status');

            // Get the order data from the view details button in the same row
            const viewDetailsButton = button.closest('tr').querySelector('[data-bs-target="#viewOrderModal"]');
            const orderData = JSON.parse(viewDetailsButton.getAttribute('data-order'));
            const paymentMethod = orderData.payment_method || 'cash';

            document.getElementById('update_order_id').value = orderId;
            document.getElementById('update_payment_method').value = paymentMethod;
            document.getElementById('update_status').value = status;
        });

        // Print Receipt Modal
        const printReceiptModal = document.getElementById('printReceiptModal');
        printReceiptModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const orderData = JSON.parse(button.getAttribute('data-order'));

            document.getElementById('receipt_order_id').textContent = orderData.id;
            document.getElementById('receipt_date').textContent = new Date(orderData.date).toLocaleString();

            // Format payment method (convert snake_case to Title Case)
            const paymentMethod = orderData.payment_method || 'cash';
            document.getElementById('receipt_payment_method').textContent = paymentMethod.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            document.getElementById('receipt_customer').textContent = orderData.user_name || 'Guest';
            document.getElementById('receipt_email').textContent = orderData.user_email || 'N/A';

            const receiptItemsContainer = document.getElementById('receipt_items');
            receiptItemsContainer.innerHTML = '';

            let total = 0;

            // Fetch order items from the normalized table using AJAX
            fetch(`get_order_items.php?order_id=${orderData.id}`)
                .then(response => response.json())
                .then(items => {
                    if (items.length > 0) {
                        // Use normalized data
                        items.forEach(item => {
                            const itemSubtotal = item.price * item.quantity;
                            total += itemSubtotal;

                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${item.name}</td>
                                <td>₱${parseFloat(item.price).toFixed(2)}</td>
                                <td>${item.quantity}</td>
                                <td>₱${itemSubtotal.toFixed(2)}</td>
                            `;
                            receiptItemsContainer.appendChild(row);
                        });
                    } else {
                        // Fall back to JSON data if no normalized data is available
                        const orderItems = JSON.parse(orderData.items);
                        orderItems.forEach(item => {
                            const itemSubtotal = item.price * item.quantity;
                            total += itemSubtotal;

                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${item.name}</td>
                                <td>₱${parseFloat(item.price).toFixed(2)}</td>
                                <td>${item.quantity}</td>
                                <td>₱${itemSubtotal.toFixed(2)}</td>
                            `;
                            receiptItemsContainer.appendChild(row);
                        });
                    }

                    document.getElementById('receipt_total').textContent = `₱${total.toFixed(2)}`;
                })
                .catch(error => {
                    console.error('Error fetching order items:', error);
                    // Fall back to JSON data if there's an error
                    const orderItems = JSON.parse(orderData.items);
                    orderItems.forEach(item => {
                        const itemSubtotal = item.price * item.quantity;
                        total += itemSubtotal;

                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${item.name}</td>
                            <td>₱${parseFloat(item.price).toFixed(2)}</td>
                            <td>${item.quantity}</td>
                            <td>₱${itemSubtotal.toFixed(2)}</td>
                        `;
                        receiptItemsContainer.appendChild(row);
                    });

                    document.getElementById('receipt_total').textContent = `₱${total.toFixed(2)}`;
                });
        });

        // Print Receipt
        function printReceipt() {
            // Get receipt information
            const orderId = document.getElementById('receipt_order_id').textContent;
            const orderDate = document.getElementById('receipt_date').textContent;
            const paymentMethod = document.getElementById('receipt_payment_method').textContent;
            const customerName = document.getElementById('receipt_customer').textContent;
            const customerEmail = document.getElementById('receipt_email').textContent;

            // Get items
            const items = [];
            const itemRows = document.querySelectorAll('#receipt_items tr');
            itemRows.forEach(row => {
                const columns = row.querySelectorAll('td');
                items.push({
                    name: columns[0].textContent,
                    price: columns[1].textContent,
                    quantity: columns[2].textContent,
                    subtotal: columns[3].textContent
                });
            });

            // Get total
            const total = document.getElementById('receipt_total').textContent;

            // Create PDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Ensure the Philippine peso symbol displays correctly
            // Use UTF-8 encoding for text
            doc.setLanguage("en-US");

            // Add restaurant info
            doc.setFontSize(20);
            doc.text("RAYMART'S DINER", 105, 20, { align: 'center' });
            doc.setFontSize(10);
            doc.text("Purok 6A, Tagbuyacan, Santiago, Agusan del Norte", 105, 30, { align: 'center' });
            doc.text("Phone: +63 ************ | Email: <EMAIL>", 105, 35, { align: 'center' });

            // Add receipt title
            doc.setFontSize(16);
            doc.text("RECEIPT", 105, 45, { align: 'center' });

            // Add order information
            doc.setFontSize(12);
            doc.text(`Receipt No: #${orderId}`, 20, 60);
            doc.text(`Date: ${orderDate}`, 20, 67);
            doc.text(`Payment Method: ${paymentMethod}`, 20, 74);
            doc.text(`Customer: ${customerName}`, 20, 81);
            doc.text(`Email: ${customerEmail}`, 20, 88);

            // Add items table
            const tableColumn = ["Item", "Price", "Quantity", "Subtotal"];
            const tableRows = [];

            items.forEach(item => {
                // Format with P for Philippine peso
                const formattedPrice = item.price.includes('P') ? item.price : `P ${parseFloat(item.price.replace(/[^\d.]/g, '')).toFixed(2)}`;
                const formattedSubtotal = item.subtotal.includes('P') ? item.subtotal : `P ${parseFloat(item.subtotal.replace(/[^\d.]/g, '')).toFixed(2)}`;

                tableRows.push([item.name, formattedPrice, item.quantity, formattedSubtotal]);
            });

            doc.autoTable({
                head: [tableColumn],
                body: tableRows,
                startY: 95,
                theme: 'grid',
                styles: {
                    fontSize: 10
                },
                headStyles: {
                    fillColor: [66, 66, 66]
                }
            });

            // Add total
            const finalY = doc.lastAutoTable.finalY + 10;

            // Format with P for Philippine peso
            const formattedTotal = total.includes('P') ? total : `P ${parseFloat(total.replace(/[^\d.]/g, '')).toFixed(2)}`;

            doc.text(`Total: ${formattedTotal}`, 150, finalY, { align: 'right' });

            // Add thank you message
            doc.setFontSize(10);
            doc.text("Thank you for dining with us!", 105, finalY + 30, { align: 'center' });
            doc.text("Please come again.", 105, finalY + 35, { align: 'center' });

            // Save the PDF
            doc.save(`Receipt_Order_${orderId}.pdf`);
        }

        // Email Receipt
        function emailReceipt() {
            const orderId = document.getElementById('receipt_order_id').textContent;
            const email = document.getElementById('receipt_email').textContent;

            if (email === 'N/A') {
                alert('No email address available for this customer.');
                return;
            }

            // In a real application, you would send an AJAX request to a PHP script
            // that would generate a PDF receipt and email it to the customer
            alert(`Receipt for Order #${orderId} will be emailed to ${email}`);
        }


    </script>
</body>
</html>
