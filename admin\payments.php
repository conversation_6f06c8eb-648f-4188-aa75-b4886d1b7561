<?php
session_start();
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header("Location: login.php");
    exit();
}

// Handle payment transaction actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Create new payment transaction
    if (isset($_POST['create_transaction'])) {
        $order_id = (int)$_POST['order_id'];
        $amount = (float)$_POST['amount'];
        $payment_method = sanitize($_POST['payment_method']);
        $transaction_status = sanitize($_POST['transaction_status']);
        $transaction_reference = sanitize($_POST['transaction_reference']);
        $notes = sanitize($_POST['notes']);

        $transaction_data = [
            'order_id' => $order_id,
            'amount' => $amount,
            'payment_method' => $payment_method,
            'transaction_status' => $transaction_status,
            'transaction_reference' => $transaction_reference,
            'notes' => $notes
        ];

        if (createPaymentTransaction($transaction_data)) {
            $_SESSION['message'] = 'Payment transaction created successfully!';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Failed to create payment transaction. Please try again.';
            $_SESSION['message_type'] = 'danger';
        }
    }
    // Update payment transaction
    else if (isset($_POST['update_transaction'])) {
        $transaction_id = (int)$_POST['transaction_id'];
        $payment_method = sanitize($_POST['payment_method']);
        $transaction_status = sanitize($_POST['transaction_status']);
        $transaction_reference = sanitize($_POST['transaction_reference']);
        $notes = sanitize($_POST['notes']);

        $transaction_data = [
            'payment_method' => $payment_method,
            'transaction_status' => $transaction_status,
            'transaction_reference' => $transaction_reference,
            'notes' => $notes
        ];

        if (updatePaymentTransaction($transaction_id, $transaction_data)) {
            $_SESSION['message'] = 'Payment transaction updated successfully!';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Failed to update payment transaction. Please try again.';
            $_SESSION['message_type'] = 'danger';
        }
    }


    // Redirect to refresh page
    header("Location: payments.php");
    exit();
}

// Get filter parameters
$transaction_status = isset($_GET['transaction_status']) ? sanitize($_GET['transaction_status']) : '';
$payment_method = isset($_GET['payment_method']) ? sanitize($_GET['payment_method']) : '';
$date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build filters array
$filters = [];
if (!empty($transaction_status)) $filters['transaction_status'] = $transaction_status;
if (!empty($payment_method)) $filters['payment_method'] = $payment_method;
if (!empty($date_from)) $filters['date_from'] = $date_from;
if (!empty($date_to)) $filters['date_to'] = $date_to;
if (!empty($search)) $filters['search'] = $search;

// Get payment transactions
$transactions = getPaymentTransactions($filters);

// Get admin information
$admin = getAdminById($_SESSION['admin_id']);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Management - <?php echo SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Admin CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/admin.css">

    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- Custom CSS for View Button and Filter Form -->
    <style>
        /* Style for buttons */

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            box-shadow: none;
            border-radius: 4px;
        }

        .btn-secondary:hover,
        .btn-secondary:focus,
        .btn-secondary:active {
            background-color: #5a6268;
            border-color: #5a6268;
            box-shadow: none !important;
            transform: none !important;
        }

        /* Filter and Reset buttons */
        .filter-form-container .btn {
            padding: 0.375rem 1rem;
            font-size: 1rem;
            line-height: 1.5;
            min-width: 80px;
            margin-bottom: 0;
        }

        /* Button container */
        .button-container {
            width: 100%;
        }

        /* Adjust button sizes */
        .filter-form-container .btn {
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Action buttons styling */
        .action-buttons {
            width: 40px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            padding: 0;
        }

        .btn-dark.action-btn {
            background-color: #212529;
        }

        .btn-warning.action-btn {
            background-color: #ffc107;
            color: #212529;
        }

        .btn-success.action-btn {
            background-color: #28a745;
        }

        .btn-danger.action-btn {
            background-color: #dc3545;
        }

        .btn-info.action-btn {
            background-color: #17a2b8;
        }

        /* Filter form styles */
        .filter-form-container {
            margin-bottom: 20px;
        }

        .filter-form-container .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
            white-space: normal;
            height: 24px;
        }

        .payment-method-label {
            margin-bottom: 0.5rem;
        }

        .payment-method-label .form-label {
            line-height: 1.2;
            height: auto;
            margin-bottom: 0;
        }

        .filter-form-container .form-control,
        .filter-form-container .form-select {
            height: 38px;
            border-radius: 4px;
        }

        .filter-form-container .btn {
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            font-weight: 500;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .filter-form-container .col-md-1 {
                margin-top: 1rem;
            }
        }

        /* Logout link styling - same as other sidebar items */
        .sidebar-menu a[href="logout.php"] {
            margin-top: 10px;
            border-top: 1px solid #343a40;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>RAYMA<span style="color: #cc1500;">R</span>T'S DINER</h3>
            <p>Admin Panel</p>
        </div>

        <div class="sidebar-menu">
            <a href="index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="menu.php"><i class="fas fa-utensils"></i> Menu Management</a>
            <a href="orders.php"><i class="fas fa-shopping-cart"></i> Order Management</a>
            <a href="payments.php" class="active"><i class="fas fa-money-bill-wave"></i> Payment Management</a>
            <a href="reservations.php"><i class="fas fa-calendar-alt"></i> Reservations</a>
            <a href="tables.php"><i class="fas fa-chair"></i> Table Management</a>
            <a href="users.php"><i class="fas fa-users"></i> User Management</a>
            <a href="logout.php" style="display: block !important; visibility: visible !important; opacity: 1 !important; color: rgba(255, 255, 255, 0.8) !important; text-decoration: none !important; padding: 12px 20px !important; background-color: #dc3545 !important; margin-top: 10px !important; border-radius: 5px !important;"><i class="fas fa-sign-out-alt" style="margin-right: 10px !important;"></i> Logout</a>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="container-fluid">
            <h1 class="mb-4">Payment Management</h1>

            <?php if (isset($_SESSION['message'])): ?>
                <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['message'], $_SESSION['message_type']); ?>
            <?php endif; ?>

            <!-- Payment Management Tabs -->
            <ul class="nav nav-tabs mb-4" id="paymentTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="transactions-tab" data-bs-toggle="tab" data-bs-target="#transactions" type="button" role="tab" aria-controls="transactions" aria-selected="true">
                        <i class="fas fa-exchange-alt"></i> Transactions
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" type="button" role="tab" aria-controls="reports" aria-selected="false">
                        <i class="fas fa-chart-bar"></i> Reports
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="paymentTabsContent">
                <!-- Transactions Tab -->
                <div class="tab-pane fade show active" id="transactions" role="tabpanel" aria-labelledby="transactions-tab">
                    <!-- Filters -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5 class="card-title">Filter Transactions</h5>
                            <form method="get" action="payments.php" class="row g-3 filter-form-container align-items-end">
                                <div class="col-md-2">
                                    <label for="transaction_status" class="form-label">Status</label>
                                    <select class="form-select" id="transaction_status" name="transaction_status">
                                        <option value="">All</option>
                                        <option value="pending" <?php echo $transaction_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="completed" <?php echo $transaction_status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                        <option value="failed" <?php echo $transaction_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <div class="payment-method-label">
                                        <label for="payment_method" class="form-label">Payment<br>Method</label>
                                    </div>
                                    <select class="form-select" id="payment_method" name="payment_method">
                                        <option value="">All</option>
                                        <option value="cash" <?php echo $payment_method === 'cash' ? 'selected' : ''; ?>>Cash</option>
                                        <option value="credit_card" <?php echo $payment_method === 'credit_card' ? 'selected' : ''; ?>>Credit Card</option>
                                        <option value="debit_card" <?php echo $payment_method === 'debit_card' ? 'selected' : ''; ?>>Debit Card</option>
                                        <option value="gcash" <?php echo $payment_method === 'gcash' ? 'selected' : ''; ?>>GCash</option>
                                        <option value="paymaya" <?php echo $payment_method === 'paymaya' ? 'selected' : ''; ?>>PayMaya</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="date_from" class="form-label">Date From</label>
                                    <input type="date" class="form-control datepicker" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                                </div>
                                <div class="col-md-2">
                                    <label for="date_to" class="form-label">Date To</label>
                                    <input type="date" class="form-control datepicker" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                                </div>
                                <div class="col-md-2">
                                    <label for="search" class="form-label">Search</label>
                                    <input type="text" class="form-control" id="search" name="search" placeholder="Reference, Customer..." value="<?php echo $search; ?>">
                                </div>
                                <div class="col-md-2">
                                    <div class="d-flex flex-column gap-2 button-container">
                                        <button type="submit" class="btn btn-primary w-100">Filter</button>
                                        <a href="payments.php" class="btn btn-secondary w-100">Reset</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Transactions Table -->
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="card-title">Payment Transactions</h5>
                                <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                                    <i class="fas fa-plus"></i> Add Transaction
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Amount</th>
                                            <th>Payment Method</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                            <th>Reference</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($transactions)): ?>
                                            <tr>
                                                <td colspan="9" class="text-center">No transactions found.</td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($transactions as $transaction): ?>
                                                <tr>
                                                    <td>#<?php echo $transaction['id']; ?></td>
                                                    <td><a href="orders.php?id=<?php echo $transaction['order_id']; ?>">#<?php echo $transaction['order_id']; ?></a></td>
                                                    <td>
                                                        <?php if ($transaction['user_id']): ?>
                                                            <?php echo $transaction['user_name']; ?><br>
                                                            <small class="text-muted"><?php echo $transaction['user_email']; ?></small>
                                                        <?php else: ?>
                                                            Guest
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo formatCurrency($transaction['amount']); ?></td>
                                                    <td><?php echo ucwords(str_replace('_', ' ', $transaction['payment_method'])); ?></td>
                                                    <td>
                                                        <?php
                                                        $status_class = '';
                                                        switch ($transaction['transaction_status']) {
                                                            case 'pending':
                                                                $status_class = 'bg-warning';
                                                                break;
                                                            case 'completed':
                                                                $status_class = 'bg-success';
                                                                break;
                                                            case 'failed':
                                                                $status_class = 'bg-danger';
                                                                break;
                                                        }
                                                        ?>
                                                        <span class="badge <?php echo $status_class; ?>">
                                                            <?php echo ucwords(str_replace('_', ' ', $transaction['transaction_status'])); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('m/d/Y g:i A', strtotime($transaction['transaction_date'])); ?></td>
                                                    <td><?php echo $transaction['transaction_reference'] ?: 'N/A'; ?></td>
                                                    <td>
                                                        <div class="d-flex flex-column gap-1 action-buttons">
                                                            <button type="button" class="btn btn-sm btn-dark action-btn" data-bs-toggle="modal" data-bs-target="#viewTransactionModal" data-transaction='<?php echo htmlspecialchars(json_encode($transaction), ENT_QUOTES, 'UTF-8'); ?>'>
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-warning action-btn" data-bs-toggle="modal" data-bs-target="#editTransactionModal" data-transaction='<?php echo htmlspecialchars(json_encode($transaction), ENT_QUOTES, 'UTF-8'); ?>'>
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Reports Tab -->
                <div class="tab-pane fade" id="reports" role="tabpanel" aria-labelledby="reports-tab">
                    <div class="row">
                        <!-- Payment Method Distribution -->
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Payment Method Distribution</h5>
                                    <canvas id="paymentMethodChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Transaction Status Distribution -->
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Transaction Status Distribution</h5>
                                    <canvas id="transactionStatusChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Monthly Transaction Volume -->
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Monthly Transaction Volume</h5>
                                    <canvas id="monthlyTransactionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Transaction Modal -->
            <div class="modal fade" id="addTransactionModal" tabindex="-1" aria-labelledby="addTransactionModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addTransactionModalLabel">Add Payment Transaction</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form method="post" action="payments.php" id="addTransactionForm">
                                <input type="hidden" name="create_transaction" value="1">

                                <div class="mb-3">
                                    <label for="order_id" class="form-label">Order ID</label>
                                    <input type="number" class="form-control" id="order_id" name="order_id" required>
                                </div>

                                <div class="mb-3">
                                    <label for="amount" class="form-label">Amount</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₱</span>
                                        <input type="number" step="0.01" class="form-control" id="amount" name="amount" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="payment_method" class="form-label">Payment Method</label>
                                    <select class="form-select" id="payment_method" name="payment_method" required>
                                        <option value="cash">Cash</option>
                                        <option value="credit_card">Credit Card</option>
                                        <option value="debit_card">Debit Card</option>
                                        <option value="gcash">GCash</option>
                                        <option value="paymaya">PayMaya</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="transaction_status" class="form-label">Status</label>
                                    <select class="form-select" id="transaction_status" name="transaction_status" required>
                                        <option value="pending">Pending</option>
                                        <option value="completed">Completed</option>
                                        <option value="failed">Failed</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="transaction_reference" class="form-label">Reference Number</label>
                                    <input type="text" class="form-control" id="transaction_reference" name="transaction_reference">
                                    <small class="text-muted">Optional. For credit card, GCash, or PayMaya transactions.</small>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" form="addTransactionForm" class="btn btn-primary">Add Transaction</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- View Transaction Modal -->
            <div class="modal fade" id="viewTransactionModal" tabindex="-1" aria-labelledby="viewTransactionModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="viewTransactionModalLabel">Transaction Details</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p><strong>Transaction ID:</strong> <span id="view_transaction_id"></span></p>
                                    <p><strong>Order ID:</strong> <span id="view_order_id"></span></p>
                                    <p><strong>Amount:</strong> <span id="view_amount"></span></p>
                                    <p><strong>Payment Method:</strong> <span id="view_payment_method"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Status:</strong> <span id="view_status"></span></p>
                                    <p><strong>Date:</strong> <span id="view_date"></span></p>
                                    <p><strong>Reference:</strong> <span id="view_reference"></span></p>
                                    <p><strong>Customer:</strong> <span id="view_customer"></span></p>
                                </div>
                            </div>
                            <div class="mb-3">
                                <p><strong>Notes:</strong></p>
                                <p id="view_notes" class="border p-2 rounded bg-light"></p>
                            </div>


                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit Transaction Modal -->
            <div class="modal fade" id="editTransactionModal" tabindex="-1" aria-labelledby="editTransactionModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editTransactionModalLabel">Edit Transaction</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form method="post" action="payments.php" id="editTransactionForm">
                                <input type="hidden" name="update_transaction" value="1">
                                <input type="hidden" name="transaction_id" id="edit_transaction_id">

                                <div class="mb-3">
                                    <label for="edit_payment_method" class="form-label">Payment Method</label>
                                    <select class="form-select" id="edit_payment_method" name="payment_method" required>
                                        <option value="cash">Cash</option>
                                        <option value="credit_card">Credit Card</option>
                                        <option value="debit_card">Debit Card</option>
                                        <option value="gcash">GCash</option>
                                        <option value="paymaya">PayMaya</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="edit_transaction_status" class="form-label">Status</label>
                                    <select class="form-select" id="edit_transaction_status" name="transaction_status" required>
                                        <option value="pending">Pending</option>
                                        <option value="completed">Completed</option>
                                        <option value="failed">Failed</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="edit_transaction_reference" class="form-label">Reference Number</label>
                                    <input type="text" class="form-control" id="edit_transaction_reference" name="transaction_reference">
                                </div>

                                <div class="mb-3">
                                    <label for="edit_notes" class="form-label">Notes</label>
                                    <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" form="editTransactionForm" class="btn btn-primary">Update Transaction</button>
                        </div>
                    </div>
                </div>
            </div>


    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Ensure logout button is visible -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/ensure-logout.js?v=<?php echo time(); ?>"></script>

    <script>
        // Initialize date pickers
        flatpickr(".datepicker", {
            dateFormat: "Y-m-d",
        });

        // View Transaction Modal
        const viewTransactionModal = document.getElementById('viewTransactionModal');
        if (viewTransactionModal) {
            viewTransactionModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const transactionData = JSON.parse(button.getAttribute('data-transaction'));

                document.getElementById('view_transaction_id').textContent = '#' + transactionData.id;
                document.getElementById('view_order_id').textContent = '#' + transactionData.order_id;
                document.getElementById('view_amount').textContent = '₱' + parseFloat(transactionData.amount).toFixed(2);
                document.getElementById('view_payment_method').textContent = transactionData.payment_method.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

                // Format status with badge
                let statusClass = '';
                switch (transactionData.transaction_status) {
                    case 'pending':
                        statusClass = 'bg-warning';
                        break;
                    case 'completed':
                        statusClass = 'bg-success';
                        break;
                    case 'failed':
                        statusClass = 'bg-danger';
                        break;
                }

                document.getElementById('view_status').innerHTML = `<span class="badge ${statusClass}">${transactionData.transaction_status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>`;
                document.getElementById('view_date').textContent = new Date(transactionData.transaction_date).toLocaleString();
                document.getElementById('view_reference').textContent = transactionData.transaction_reference || 'N/A';

                // Customer information
                let customerInfo = 'Guest';
                if (transactionData.user_name) {
                    customerInfo = transactionData.user_name;
                    if (transactionData.user_email) {
                        customerInfo += ` (${transactionData.user_email})`;
                    }
                }
                document.getElementById('view_customer').textContent = customerInfo;

                // Notes
                document.getElementById('view_notes').textContent = transactionData.notes || 'No notes available.';
            });
        }

        // Edit Transaction Modal
        const editTransactionModal = document.getElementById('editTransactionModal');
        if (editTransactionModal) {
            editTransactionModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const transactionData = JSON.parse(button.getAttribute('data-transaction'));

                document.getElementById('edit_transaction_id').value = transactionData.id;
                document.getElementById('edit_payment_method').value = transactionData.payment_method;
                document.getElementById('edit_transaction_status').value = transactionData.transaction_status;
                document.getElementById('edit_transaction_reference').value = transactionData.transaction_reference || '';
                document.getElementById('edit_notes').value = transactionData.notes || '';
            });
        }

        // Initialize charts for the Reports tab
        document.getElementById('reports-tab').addEventListener('shown.bs.tab', function (e) {
            // Payment Method Distribution Chart - Fetch real data from PHP
            <?php
            // Query to get payment method distribution
            $query = "SELECT
                        payment_method,
                        COUNT(*) as count
                      FROM payment_transactions
                      GROUP BY payment_method";

            $stmt = $conn->prepare($query);
            $stmt->execute();
            $result = $stmt->get_result();

            $payment_methods = [];
            $payment_counts = [];
            $payment_colors = [
                'cash' => 'rgba(75, 192, 192, 0.7)',
                'credit_card' => 'rgba(54, 162, 235, 0.7)',
                'debit_card' => 'rgba(153, 102, 255, 0.7)',
                'gcash' => 'rgba(255, 159, 64, 0.7)',
                'paymaya' => 'rgba(255, 99, 132, 0.7)'
            ];
            $colors = [];

            while ($row = $result->fetch_assoc()) {
                $method = ucwords(str_replace('_', ' ', $row['payment_method']));
                $payment_methods[] = $method;
                $payment_counts[] = (int)$row['count'];
                $colors[] = $payment_colors[$row['payment_method']] ?? 'rgba(128, 128, 128, 0.7)';
            }

            // If no data, provide default values
            if (empty($payment_methods)) {
                $payment_methods = ['Cash', 'Credit Card', 'Debit Card', 'GCash', 'PayMaya'];
                $payment_counts = [0, 0, 0, 0, 0];
                $colors = array_values($payment_colors);
            }
            ?>

            const paymentMethodCtx = document.getElementById('paymentMethodChart').getContext('2d');
            const paymentMethodChart = new Chart(paymentMethodCtx, {
                type: 'pie',
                data: {
                    labels: <?php echo json_encode($payment_methods); ?>,
                    datasets: [{
                        data: <?php echo json_encode($payment_counts); ?>,
                        backgroundColor: <?php echo json_encode($colors); ?>,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            // Transaction Status Distribution Chart - Fetch real data from PHP
            <?php
            // Query to get transaction status distribution
            $query = "SELECT
                        transaction_status,
                        COUNT(*) as count
                      FROM payment_transactions
                      GROUP BY transaction_status";

            $stmt = $conn->prepare($query);
            $stmt->execute();
            $result = $stmt->get_result();

            $status_labels = [];
            $status_counts = [];
            $status_colors = [
                'pending' => 'rgba(255, 205, 86, 0.7)',
                'completed' => 'rgba(75, 192, 192, 0.7)',
                'failed' => 'rgba(255, 99, 132, 0.7)'
            ];
            $status_bg_colors = [];

            while ($row = $result->fetch_assoc()) {
                $status = ucwords(str_replace('_', ' ', $row['transaction_status']));
                $status_labels[] = $status;
                $status_counts[] = (int)$row['count'];
                $status_bg_colors[] = $status_colors[$row['transaction_status']] ?? 'rgba(128, 128, 128, 0.7)';
            }

            // If no data, provide default values
            if (empty($status_labels)) {
                $status_labels = ['Pending', 'Completed', 'Failed'];
                $status_counts = [0, 0, 0];
                $status_bg_colors = array_values($status_colors);
            }
            ?>

            const transactionStatusCtx = document.getElementById('transactionStatusChart').getContext('2d');
            const transactionStatusChart = new Chart(transactionStatusCtx, {
                type: 'doughnut',
                data: {
                    labels: <?php echo json_encode($status_labels); ?>,
                    datasets: [{
                        data: <?php echo json_encode($status_counts); ?>,
                        backgroundColor: <?php echo json_encode($status_bg_colors); ?>,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            // Monthly Transaction Volume Chart - Fetch real data from PHP
            <?php
            // Get transaction data for the current month
            $current_month = date('Y-m');
            $current_month_name = date('F Y');

            // Query to get transaction volume for the current month
            $query = "SELECT SUM(amount) as total_amount
                      FROM payment_transactions
                      WHERE DATE_FORMAT(transaction_date, '%Y-%m') = ?
                      AND transaction_status != 'failed'";

            $stmt = $conn->prepare($query);
            $stmt->bind_param("s", $current_month);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();

            $current_month_volume = $row['total_amount'] ? (float)$row['total_amount'] : 0;
            ?>

            const monthlyTransactionCtx = document.getElementById('monthlyTransactionChart').getContext('2d');
            const monthlyTransactionChart = new Chart(monthlyTransactionCtx, {
                type: 'bar',
                data: {
                    labels: ['<?php echo $current_month_name; ?>'],
                    datasets: [{
                        label: 'Transaction Volume',
                        data: [<?php echo $current_month_volume; ?>],
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₱' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Volume: ₱' + context.raw.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });

            // Create a refresh button for all charts
            const refreshAllChartsBtn = document.createElement('button');
            refreshAllChartsBtn.className = 'btn btn-sm btn-primary position-fixed';
            refreshAllChartsBtn.style.bottom = '20px';
            refreshAllChartsBtn.style.right = '20px';
            refreshAllChartsBtn.style.zIndex = '1000';
            refreshAllChartsBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Charts';
            document.body.appendChild(refreshAllChartsBtn);

            // Function to refresh the page when the button is clicked
            refreshAllChartsBtn.addEventListener('click', function() {
                location.reload();
            });

            // Auto-refresh the page every 30 seconds to get real-time data
            const autoRefresh = setInterval(function() {
                // Only refresh if the Reports tab is active
                if (document.getElementById('reports-tab').classList.contains('active')) {
                    location.reload();
                }
            }, 30000);
        });
    </script>
</body>
</html>