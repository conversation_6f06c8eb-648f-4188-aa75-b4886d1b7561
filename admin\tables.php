<?php
session_start();
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header("Location: login.php");
    exit();
}

// Handle table actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Add new table
    if (isset($_POST['add_table'])) {
        $capacity = (int)$_POST['capacity'];
        $is_reserved = isset($_POST['is_reserved']) ? 1 : 0;

        // Validate form data
        $errors = [];

        if ($capacity < 1) {
            $errors[] = 'Capacity must be at least 1!';
        }

        // If no errors, add table
        if (empty($errors)) {
            $stmt = $conn->prepare("INSERT INTO tables (capacity, is_reserved) VALUES (?, ?)");
            $stmt->bind_param("ii", $capacity, $is_reserved);

            if ($stmt->execute()) {
                // Set success message
                $_SESSION['message'] = 'Table added successfully!';
                $_SESSION['message_type'] = 'success';
            } else {
                // Set error message
                $_SESSION['message'] = 'Failed to add table. Please try again.';
                $_SESSION['message_type'] = 'danger';
            }
        } else {
            // Set error message
            $_SESSION['message'] = implode('<br>', $errors);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Update table
    else if (isset($_POST['update_table'])) {
        $table_id = (int)$_POST['table_id'];
        $capacity = (int)$_POST['capacity'];
        $is_reserved = isset($_POST['is_reserved']) ? 1 : 0;

        // Validate form data
        $errors = [];

        if ($capacity < 1) {
            $errors[] = 'Capacity must be at least 1!';
        }

        // If no errors, update table
        if (empty($errors)) {
            $stmt = $conn->prepare("UPDATE tables SET capacity = ?, is_reserved = ? WHERE id = ?");
            $stmt->bind_param("iii", $capacity, $is_reserved, $table_id);

            if ($stmt->execute()) {
                // Set success message
                $_SESSION['message'] = 'Table updated successfully!';
                $_SESSION['message_type'] = 'success';
            } else {
                // Set error message
                $_SESSION['message'] = 'Failed to update table. Please try again.';
                $_SESSION['message_type'] = 'danger';
            }
        } else {
            // Set error message
            $_SESSION['message'] = implode('<br>', $errors);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Delete table
    else if (isset($_POST['delete_table'])) {
        $table_id = (int)$_POST['table_id'];

        $stmt = $conn->prepare("DELETE FROM tables WHERE id = ?");
        $stmt->bind_param("i", $table_id);

        if ($stmt->execute()) {
            // Set success message
            $_SESSION['message'] = 'Table deleted successfully!';
            $_SESSION['message_type'] = 'success';
        } else {
            // Set error message
            $_SESSION['message'] = 'Failed to delete table. Please try again.';
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Redirect to refresh page
    header("Location: tables.php");
    exit();
}

// Get current date
$current_date = date('Y-m-d');

// Get all tables with their reservation status for today
$query = "
    SELECT t.*,
           CASE WHEN rt.table_id IS NOT NULL THEN 1 ELSE 0 END AS is_reserved_today,
           r.id AS reservation_id,
           r.time AS reservation_time,
           r.guests AS reservation_guests,
           r.status AS reservation_status,
           u.name AS customer_name,
           GROUP_CONCAT(
               DISTINCT CONCAT(r2.id, ':', r2.time, ':', r2.guests, ':', r2.status, ':', COALESCE(u2.name, 'Guest'))
               ORDER BY r2.time ASC
               SEPARATOR '|'
           ) AS all_reservations
    FROM tables t
    LEFT JOIN reservation_tables rt ON t.id = rt.table_id
    LEFT JOIN reservations r ON rt.reservation_id = r.id AND r.date = ? AND r.status IN ('pending', 'confirmed')
    LEFT JOIN users u ON r.user_id = u.id
    LEFT JOIN reservation_tables rt2 ON t.id = rt2.table_id
    LEFT JOIN reservations r2 ON rt2.reservation_id = r2.id AND r2.date = ? AND r2.status IN ('pending', 'confirmed')
    LEFT JOIN users u2 ON r2.user_id = u2.id
    GROUP BY t.id
    ORDER BY t.id
";

$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $current_date, $current_date);
$stmt->execute();
$result = $stmt->get_result();

$tables = [];
while ($row = $result->fetch_assoc()) {
    $tables[] = $row;
}

// Get table statistics
$stmt = $conn->prepare("SELECT COUNT(*) as total_tables FROM tables");
$stmt->execute();
$result = $stmt->get_result();
$total_tables = $result->fetch_assoc()['total_tables'];

// Count tables reserved for today
$reserved_tables = 0;
foreach ($tables as $table) {
    if ($table['is_reserved_today']) {
        $reserved_tables++;
    }
}

$available_tables = $total_tables - $reserved_tables;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Management - <?php echo SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Admin CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/admin.css">

    <!-- Hide Cart Badges CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/admin/assets/css/hide-cart-badges.css">

    <!-- Custom CSS for Tables and Buttons -->
    <style>
        /* Hide cart badges directly in this page */
        .cart-badge,
        .cart-icon-container .badge,
        .badge.bg-warning:not(.reservation-item .badge),
        .badge.bg-danger:not(.table-card .badge),
        span[class*="badge"][style*="display: flex"],
        .fa-shopping-cart + .badge {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            z-index: -9999 !important;
        }

        /* Style for Edit buttons */
        .table-card .btn-dark {
            background-color: #212529;
            border-color: #212529;
            box-shadow: none;
        }

        .table-card .btn-dark:hover,
        .table-card .btn-dark:focus,
        .table-card .btn-dark:active {
            background-color: #1a1e21;
            border-color: #1a1e21;
            box-shadow: none !important;
            transform: none !important;
        }

        /* Update button in modal */
        #editTableModal .btn-dark {
            background-color: #212529;
            border-color: #212529;
            box-shadow: none;
        }

        #editTableModal .btn-dark:hover,
        #editTableModal .btn-dark:focus,
        #editTableModal .btn-dark:active {
            background-color: #1a1e21;
            border-color: #1a1e21;
            box-shadow: none !important;
            transform: none !important;
        }

        /* Reservation info styling */
        .reservation-info {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .reservation-item {
            position: relative;
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 3px solid #0d6efd;
        }

        .reservation-item p {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .reservation-item strong {
            color: #0d6efd;
        }

        /* Make ID number more prominent */
        .reservation-item p strong {
            font-size: 1.1rem;
        }



        .reservation-item .badge {
            font-size: 0.85rem;
            padding: 5px 8px;
        }



        .reservation-item:last-child {
            border-bottom: none !important;
            padding-bottom: 10px !important;
            margin-bottom: 0 !important;
        }

        .reservation-item .badge {
            position: absolute;
            top: 5px;
            right: 5px;
        }

        /* Time slot indicator */
        .time-slot-indicator {
            font-size: 0.9rem;
            color: #198754;
            margin-top: 8px;
            text-align: center;
            background-color: rgba(25, 135, 84, 0.1);
            padding: 5px;
            border-radius: 4px;
            font-weight: 500;
        }

        .table-card.reserved {
            border-left: 4px solid #dc3545;
        }

        .table-card.available {
            border-left: 4px solid #198754;
        }

        /* Add a refresh button */
        .refresh-button {
            margin-left: 10px;
            background-color: transparent;
            border: none;
            color: #0d6efd;
            cursor: pointer;
        }

        .refresh-button:hover {
            color: #0a58ca;
        }

        /* Highlight effect for focused table */
        .highlight-table {
            animation: highlight-pulse 2s ease-in-out;
            box-shadow: 0 0 15px rgba(13, 110, 253, 0.5);
        }

        @keyframes highlight-pulse {
            0% { box-shadow: 0 0 15px rgba(13, 110, 253, 0.5); }
            50% { box-shadow: 0 0 25px rgba(13, 110, 253, 0.8); }
            100% { box-shadow: 0 0 15px rgba(13, 110, 253, 0.5); }
        }

        /* Logout link styling - same as other sidebar items */
        .sidebar-menu a[href="logout.php"] {
            margin-top: 10px;
            border-top: 1px solid #343a40;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>RAYMA<span style="color: #cc1500;">R</span>T'S DINER</h3>
            <p>Admin Panel</p>
        </div>

        <div class="sidebar-menu">
            <a href="index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="menu.php"><i class="fas fa-utensils"></i> Menu Management</a>
            <a href="orders.php"><i class="fas fa-shopping-cart"></i> Order Management</a>
            <a href="payments.php"><i class="fas fa-money-bill-wave"></i> Payment Management</a>
            <a href="reservations.php"><i class="fas fa-calendar-alt"></i> Reservations</a>
            <a href="tables.php" class="active"><i class="fas fa-chair"></i> Table Management</a>
            <a href="users.php"><i class="fas fa-users"></i> User Management</a>
            <a href="logout.php" style="display: block !important; visibility: visible !important; opacity: 1 !important; color: rgba(255, 255, 255, 0.8) !important; text-decoration: none !important; padding: 12px 20px !important; background-color: #dc3545 !important; margin-top: 10px !important; border-radius: 5px !important;"><i class="fas fa-sign-out-alt" style="margin-right: 10px !important;"></i> Logout</a>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="d-flex align-items-center">
                    <h1>Table Management</h1>
                    <button type="button" class="refresh-button" id="refreshButton" title="Refresh table status">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <div class="ms-3">
                        <span class="badge bg-secondary">Date: <span id="current-date"><?php echo date('F j, Y', strtotime($current_date)); ?></span></span>
                        <span class="badge bg-info ms-2">Last updated: <span id="last-updated"><?php echo date('h:i:s A'); ?></span></span>
                    </div>
                </div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTableModal">
                    <i class="fas fa-plus"></i> Add New Table
                </button>
            </div>

            <?php if (isset($_SESSION['message'])): ?>
                <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stat-card">
                        <i class="fas fa-chair text-primary"></i>
                        <h3 id="total-tables"><?php echo $total_tables; ?></h3>
                        <p>Total Tables</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="stat-card">
                        <i class="fas fa-check-circle text-success"></i>
                        <h3 id="available-tables"><?php echo $available_tables; ?></h3>
                        <p>Available Tables</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="stat-card">
                        <i class="fas fa-times-circle text-danger"></i>
                        <h3 id="reserved-tables"><?php echo $reserved_tables; ?></h3>
                        <p>Reserved Tables</p>
                    </div>
                </div>
            </div>

            <!-- Tables -->
            <div class="row">
                <?php if (empty($tables)): ?>
                    <div class="col-12">
                        <div class="alert alert-info">
                            No tables found. Click the "Add New Table" button to add a table.
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($tables as $table): ?>
                        <div class="col-md-4 mb-4" id="table-container-<?php echo $table['id']; ?>">
                            <div class="table-card <?php echo $table['is_reserved_today'] ? 'reserved' : 'available'; ?> <?php echo isset($_GET['focus_table']) && $_GET['focus_table'] == $table['id'] ? 'highlight-table' : ''; ?>">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5>Table #<?php echo $table['id']; ?></h5>
                                    <span class="badge <?php echo $table['is_reserved_today'] ? 'bg-danger' : 'bg-success'; ?>">
                                        <?php echo $table['is_reserved_today'] ? 'Reserved Today' : 'Available Today'; ?>
                                    </span>
                                </div>

                                <p><strong>Capacity:</strong> <?php echo $table['capacity']; ?> <?php echo $table['capacity'] > 1 ? 'persons' : 'person'; ?></p>

                                <!-- Reservation information will be loaded via AJAX -->
                                <div id="reservation-container-<?php echo $table['id']; ?>" class="reservation-container mt-2 mb-3">
                                    <?php if ($table['is_reserved_today']): ?>
                                        <div class="text-center py-3">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2 mb-0">Loading reservation data...</p>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="d-flex justify-content-end mt-3">
                                    <button type="button" class="btn btn-sm btn-dark me-2" data-bs-toggle="modal" data-bs-target="#editTableModal" data-table-id="<?php echo $table['id']; ?>" data-capacity="<?php echo $table['capacity']; ?>" data-is-reserved="<?php echo $table['is_reserved']; ?>">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteTableModal" data-table-id="<?php echo $table['id']; ?>">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Table Modal -->
    <div class="modal fade" id="addTableModal" tabindex="-1" aria-labelledby="addTableModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTableModalLabel">Add New Table</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="tables.php">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="capacity" class="form-label">Capacity</label>
                            <input type="number" class="form-control" id="capacity" name="capacity" min="1" required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_reserved" name="is_reserved">
                            <label class="form-check-label" for="is_reserved">Is Reserved</label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" name="add_table">Add Table</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Table Modal -->
    <div class="modal fade" id="editTableModal" tabindex="-1" aria-labelledby="editTableModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTableModalLabel">Edit Table</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="tables.php">
                    <div class="modal-body">
                        <input type="hidden" id="edit_table_id" name="table_id">

                        <div class="mb-3">
                            <label for="edit_capacity" class="form-label">Capacity</label>
                            <input type="number" class="form-control" id="edit_capacity" name="capacity" min="1" required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="edit_is_reserved" name="is_reserved">
                            <label class="form-check-label" for="edit_is_reserved">Is Reserved</label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-dark" name="update_table">Update Table</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Table Modal -->
    <div class="modal fade" id="deleteTableModal" tabindex="-1" aria-labelledby="deleteTableModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteTableModalLabel">Delete Table</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this table?</p>
                    <p class="text-danger">This action cannot be undone!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="post" action="tables.php">
                        <input type="hidden" id="delete_table_id" name="table_id">
                        <button type="submit" class="btn btn-danger" name="delete_table">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Admin Cart Override - Prevents cart badges -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/admin-cart-override.js"></script>

    <!-- Ensure logout button is visible -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/ensure-logout.js?v=<?php echo time(); ?>"></script>

    <script>
        // Edit Table Modal
        const editTableModal = document.getElementById('editTableModal');
        editTableModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const tableId = button.getAttribute('data-table-id');
            const capacity = button.getAttribute('data-capacity');
            const isReserved = button.getAttribute('data-is-reserved') === '1';

            document.getElementById('edit_table_id').value = tableId;
            document.getElementById('edit_capacity').value = capacity;
            document.getElementById('edit_is_reserved').checked = isReserved;
        });

        // Delete Table Modal
        const deleteTableModal = document.getElementById('deleteTableModal');
        deleteTableModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const tableId = button.getAttribute('data-table-id');

            document.getElementById('delete_table_id').value = tableId;
        });

        // Function to load table data via AJAX
        function loadTableData(tableId = null) {
            const url = tableId ?
                `<?php echo SITE_URL; ?>/admin/get_table_data.php?table_id=${tableId}` :
                '<?php echo SITE_URL; ?>/admin/get_table_data.php';

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (tableId) {
                        // Update a single table
                        updateTableCard(data.tables);
                    } else {
                        // Update all tables
                        data.tables.forEach(table => {
                            updateTableCard(table);
                        });

                        // Update statistics
                        document.getElementById('total-tables').textContent = data.stats.total_tables;
                        document.getElementById('available-tables').textContent = data.stats.available_tables;
                        document.getElementById('reserved-tables').textContent = data.stats.reserved_tables;

                        // Update timestamp
                        document.getElementById('last-updated').textContent = data.timestamp;
                        document.getElementById('current-date').textContent = data.date;
                    }
                })
                .catch(error => {
                    console.error('Error fetching table data:', error);
                });
        }

        // Function to update a table card with new data
        function updateTableCard(table) {
            const reservationContainer = document.getElementById(`reservation-container-${table.id}`);
            if (!reservationContainer) return;

            // Debug time data
            if (table.reservations && table.reservations.length > 0) {
                console.log('Reservation time data for table #' + table.id + ':',
                    table.reservations.map(r => ({ id: r.id, time: r.time })));
            }

            if (table.is_reserved_today && table.reservations && table.reservations.length > 0) {
                let html = `
                    <div class="reservation-info">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Today's Reservations:
                                <small class="text-muted">(Last updated: ${new Date().toLocaleTimeString()})</small>
                            </h6>
                            <button type="button" class="btn btn-sm btn-outline-primary refresh-table-btn" data-table-id="${table.id}">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                `;

                table.reservations.forEach(reservation => {
                    html += `
                        <div class="reservation-item mb-2 pb-2 border-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <p class="mb-1">
                                    <strong>ID:</strong> #${reservation.id}
                                </p>
                                <span class="badge ${reservation.status === 'confirmed' ? 'bg-success' : 'bg-warning'}">
                                    ${reservation.status.charAt(0).toUpperCase() + reservation.status.slice(1)}
                                </span>
                            </div>
                        </div>
                    `;
                });

                html += '</div>';
                reservationContainer.innerHTML = html;

                // Add event listeners to the new refresh buttons
                reservationContainer.querySelectorAll('.refresh-table-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        refreshTable(this.getAttribute('data-table-id'));
                    });
                });
            } else if (table.is_reserved_today) {
                reservationContainer.innerHTML = `
                    <div class="reservation-info">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Today's Reservations:
                                <small class="text-muted">(Last updated: ${new Date().toLocaleTimeString()})</small>
                            </h6>
                            <button type="button" class="btn btn-sm btn-outline-primary refresh-table-btn" data-table-id="${table.id}">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div class="reservation-item mb-2 pb-2 border-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <p class="mb-1">
                                    <strong>ID:</strong> #${table.reservation_id}
                                </p>
                                <span class="badge ${table.reservation_status === 'confirmed' ? 'bg-success' : 'bg-warning'}">
                                    ${table.reservation_status.charAt(0).toUpperCase() + table.reservation_status.slice(1)}
                                </span>
                            </div>
                        </div>
                    </div>
                `;

                // Add event listeners to the new refresh buttons
                reservationContainer.querySelectorAll('.refresh-table-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        refreshTable(this.getAttribute('data-table-id'));
                    });
                });
            } else {
                reservationContainer.innerHTML = '';
            }
        }

        // Function to refresh a specific table
        function refreshTable(tableId) {
            const container = document.getElementById(`reservation-container-${tableId}`);
            if (container) {
                container.innerHTML = `
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 mb-0">Loading reservation data...</p>
                    </div>
                `;

                // Add spinning animation to the refresh button in the main refresh button
                const mainRefreshIcon = document.getElementById('refreshButton').querySelector('i');
                if (mainRefreshIcon) {
                    mainRefreshIcon.classList.add('fa-spin');
                    setTimeout(() => {
                        mainRefreshIcon.classList.remove('fa-spin');
                    }, 1000);
                }

                // Load the table data
                loadTableData(tableId);
            }
        }

        // Format time function for client-side - exact format as selected by client
        function formatTime(timeString) {
            if (!timeString) return '';
            const [hours, minutes] = timeString.split(':');
            const hour = parseInt(hours);
            const ampm = hour >= 12 ? 'PM' : 'AM';
            const hour12 = hour % 12 || 12;
            return `${hour12}:${minutes} ${ampm}`;
        }

        // Format time exactly as "3:30 PM" (single line)
        function formatExactTime(timeString) {
            if (!timeString) return 'No time';

            try {
                // Handle different time formats
                let parts = timeString.split(':');
                if (parts.length < 2) {
                    console.error('Invalid time format:', timeString);
                    return timeString; // Return original if format is unexpected
                }

                const hours = parts[0];
                const minutes = parts[1].substring(0, 2); // Ensure we only get the minutes part

                const hour = parseInt(hours);
                if (isNaN(hour)) {
                    console.error('Invalid hour:', hours);
                    return timeString; // Return original if hour is not a number
                }

                const ampm = hour >= 12 ? 'PM' : 'AM';
                const hour12 = hour % 12 || 12;

                // Format exactly as "3:30 PM" (single line)
                return `${hour12}:${minutes.padStart(2, '0')} ${ampm}`;
            } catch (error) {
                console.error('Error formatting time:', error, timeString);
                return timeString || 'No time';
            }
        }



        // Refresh button functionality
        document.getElementById('refreshButton').addEventListener('click', function() {
            // Add spinning animation to the refresh icon
            const refreshIcon = this.querySelector('i');
            refreshIcon.classList.add('fa-spin');

            // Load all table data
            loadTableData();

            // Remove spinning animation after a delay
            setTimeout(() => {
                refreshIcon.classList.remove('fa-spin');
            }, 1000);
        });

        // Auto-refresh data every 10 seconds to show real-time data
        let refreshTimer = setInterval(function() {
            loadTableData();
        }, 10000);

        // Load table data when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadTableData();

            // Remove any cart badges that might be dynamically added
            function removeCartBadges() {
                // Find all badges with class bg-warning that are not part of reservation items
                const cartBadges = document.querySelectorAll('.badge.bg-warning:not(.reservation-item .badge)');
                cartBadges.forEach(badge => {
                    badge.remove();
                });

                // Also remove any badges inside cart icon containers
                const containerBadges = document.querySelectorAll('.cart-icon-container .badge');
                containerBadges.forEach(badge => {
                    badge.remove();
                });
            }

            // Run immediately
            removeCartBadges();

            // Also run periodically to catch any dynamically added badges
            setInterval(removeCartBadges, 1000);
        });

        // Scroll to focused table if URL parameter is present
        if (window.location.href.includes('focus_table=')) {
            const urlParams = new URLSearchParams(window.location.search);
            const focusTableId = urlParams.get('focus_table');
            if (focusTableId) {
                const tableElement = document.getElementById('table-container-' + focusTableId);
                if (tableElement) {
                    setTimeout(function() {
                        tableElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }, 500);
                }
            }
        }
    </script>
</body>
</html>
