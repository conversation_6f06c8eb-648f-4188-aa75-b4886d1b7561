<?php
session_start();
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header("Location: login.php");
    exit();
}

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Delete user
    if (isset($_POST['delete_user'])) {
        $user_id = (int)$_POST['user_id'];

        $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);

        if ($stmt->execute()) {
            // Set success message
            $_SESSION['message'] = 'User deleted successfully!';
            $_SESSION['message_type'] = 'success';
        } else {
            // Set error message
            $_SESSION['message'] = 'Failed to delete user. Please try again.';
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Redirect to refresh page
    header("Location: users.php");
    exit();
}

// Get search parameter
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query
$query = "SELECT * FROM users WHERE 1=1";
$params = [];
$types = "";

if (!empty($search)) {
    $search_term = "%$search%";
    $query .= " AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $types .= "sss";
}

$query .= " ORDER BY name";

// Prepare and execute query
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

$users = [];
while ($row = $result->fetch_assoc()) {
    $users[] = $row;
}

// Get user statistics
$stmt = $conn->prepare("SELECT COUNT(*) as total_users FROM users");
$stmt->execute();
$result = $stmt->get_result();
$total_users = $result->fetch_assoc()['total_users'];

$stmt = $conn->prepare("SELECT COUNT(DISTINCT user_id) as users_with_orders FROM orders WHERE user_id IS NOT NULL");
$stmt->execute();
$result = $stmt->get_result();
$users_with_orders = $result->fetch_assoc()['users_with_orders'];

$stmt = $conn->prepare("SELECT COUNT(DISTINCT user_id) as users_with_reservations FROM reservations WHERE user_id IS NOT NULL");
$stmt->execute();
$result = $stmt->get_result();
$users_with_reservations = $result->fetch_assoc()['users_with_reservations'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - <?php echo SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Admin CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/admin.css">

    <!-- Custom CSS for View Button -->
    <style>
        /* Style for View button */
        .btn-dark {
            background-color: #212529;
            border-color: #212529;
            box-shadow: none;
        }

        .btn-dark:hover,
        .btn-dark:focus,
        .btn-dark:active {
            background-color: #1a1e21;
            border-color: #1a1e21;
            box-shadow: none !important;
            transform: none !important;
        }

        /* Keep delete button styling */
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        /* Logout link styling - same as other sidebar items */
        .sidebar-menu a[href="logout.php"] {
            margin-top: 10px;
            border-top: 1px solid #343a40;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>RAYMA<span style="color: #cc1500;">R</span>T'S DINER</h3>
            <p>Admin Panel</p>
        </div>

        <div class="sidebar-menu">
            <a href="index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="menu.php"><i class="fas fa-utensils"></i> Menu Management</a>
            <a href="orders.php"><i class="fas fa-shopping-cart"></i> Order Management</a>
            <a href="payments.php"><i class="fas fa-money-bill-wave"></i> Payment Management</a>
            <a href="reservations.php"><i class="fas fa-calendar-alt"></i> Reservations</a>
            <a href="tables.php"><i class="fas fa-chair"></i> Table Management</a>
            <a href="users.php" class="active"><i class="fas fa-users"></i> User Management</a>
            <a href="logout.php" style="margin-top: 10px; border-top: 1px solid #343a40; padding-top: 15px;"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="container-fluid">
            <h1 class="mb-4">User Management</h1>

            <?php if (isset($_SESSION['message'])): ?>
                <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stat-card">
                        <i class="fas fa-users text-primary"></i>
                        <h3><?php echo $total_users; ?></h3>
                        <p>Total Users</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="stat-card">
                        <i class="fas fa-shopping-cart text-success"></i>
                        <h3><?php echo $users_with_orders; ?></h3>
                        <p>Users with Orders</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="stat-card">
                        <i class="fas fa-calendar-alt text-info"></i>
                        <h3><?php echo $users_with_reservations; ?></h3>
                        <p>Users with Reservations</p>
                    </div>
                </div>
            </div>

            <!-- Search -->
            <div class="filter-card">
                <form method="get" action="users.php" class="row g-3">
                    <div class="col-md-10">
                        <input type="text" class="form-control" id="search" name="search" placeholder="Search by name, email, or phone" value="<?php echo $search; ?>">
                    </div>

                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">Search</button>
                    </div>
                </form>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Address</th>
                                    <th>Registered</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center">No users found.</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td><?php echo $user['id']; ?></td>
                                            <td><?php echo $user['name']; ?></td>
                                            <td><?php echo $user['email']; ?></td>
                                            <td><?php echo $user['phone'] ? $user['phone'] : 'N/A'; ?></td>
                                            <td><?php echo $user['address'] ? (strlen($user['address']) > 30 ? substr($user['address'], 0, 30) . '...' : $user['address']) : 'N/A'; ?></td>
                                            <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-dark" data-bs-toggle="modal" data-bs-target="#viewUserModal" data-user='<?php echo htmlspecialchars(json_encode($user), ENT_QUOTES, 'UTF-8'); ?>'>
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteUserModal" data-user-id="<?php echo $user['id']; ?>" data-user-name="<?php echo $user['name']; ?>">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- View User Modal -->
    <div class="modal fade" id="viewUserModal" tabindex="-1" aria-labelledby="viewUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewUserModalLabel">User Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>ID:</strong> <span id="user_id"></span></p>
                            <p><strong>Name:</strong> <span id="user_name"></span></p>
                            <p><strong>Email:</strong> <span id="user_email"></span></p>
                            <p><strong>Phone:</strong> <span id="user_phone"></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Address:</strong> <span id="user_address"></span></p>
                            <p><strong>Registered:</strong> <span id="user_created_at"></span></p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>Orders</h6>
                            <div id="user_orders" class="list-group">
                                <!-- Orders will be populated here -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Reservations</h6>
                            <div id="user_reservations" class="list-group">
                                <!-- Reservations will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete User Modal -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteUserModalLabel">Delete User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete <span id="delete_user_name"></span>?</p>
                    <p class="text-danger">This action cannot be undone! All orders and reservations associated with this user will be affected.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="post" action="users.php">
                        <input type="hidden" id="delete_user_id" name="user_id">
                        <button type="submit" class="btn btn-danger" name="delete_user">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Ensure logout button is visible -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/ensure-logout.js?v=<?php echo time(); ?>"></script>

    <script>
        // View User Modal
        const viewUserModal = document.getElementById('viewUserModal');
        viewUserModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const userData = JSON.parse(button.getAttribute('data-user'));

            document.getElementById('user_id').textContent = userData.id;
            document.getElementById('user_name').textContent = userData.name;
            document.getElementById('user_email').textContent = userData.email;
            document.getElementById('user_phone').textContent = userData.phone || 'N/A';
            document.getElementById('user_address').textContent = userData.address || 'N/A';
            document.getElementById('user_created_at').textContent = new Date(userData.created_at).toLocaleString();

            // Fetch user orders
            fetch(`get_user_data.php?type=orders&user_id=${userData.id}`)
                .then(response => response.json())
                .then(data => {
                    const ordersContainer = document.getElementById('user_orders');
                    ordersContainer.innerHTML = '';

                    if (data.length === 0) {
                        ordersContainer.innerHTML = '<p class="text-muted">No orders found.</p>';
                    } else {
                        data.forEach(order => {
                            const orderItem = document.createElement('a');
                            orderItem.href = `orders.php?search=${userData.email}`;
                            orderItem.className = 'list-group-item list-group-item-action';
                            orderItem.innerHTML = `
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Order #${order.id}</h6>
                                    <small>${new Date(order.date).toLocaleDateString()}</small>
                                </div>
                                <p class="mb-1">Total: ${formatCurrency(order.total_price)}</p>
                                <small class="text-muted">Status: ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}</small>
                            `;
                            ordersContainer.appendChild(orderItem);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching orders:', error);
                    document.getElementById('user_orders').innerHTML = '<p class="text-danger">Error loading orders.</p>';
                });

            // Fetch user reservations
            fetch(`get_user_data.php?type=reservations&user_id=${userData.id}`)
                .then(response => response.json())
                .then(data => {
                    const reservationsContainer = document.getElementById('user_reservations');
                    reservationsContainer.innerHTML = '';

                    if (data.length === 0) {
                        reservationsContainer.innerHTML = '<p class="text-muted">No reservations found.</p>';
                    } else {
                        data.forEach(reservation => {
                            const reservationItem = document.createElement('a');
                            reservationItem.href = `reservations.php?search=${userData.email}`;
                            reservationItem.className = 'list-group-item list-group-item-action';
                            reservationItem.innerHTML = `
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Reservation #${reservation.id}</h6>
                                    <small>${new Date(reservation.date).toLocaleDateString()}</small>
                                </div>
                                <p class="mb-1">Time: ${formatTime(reservation.time)}</p>
                                <small class="text-muted">Guests: ${reservation.guests} | Status: ${reservation.status.charAt(0).toUpperCase() + reservation.status.slice(1)}</small>
                            `;
                            reservationsContainer.appendChild(reservationItem);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching reservations:', error);
                    document.getElementById('user_reservations').innerHTML = '<p class="text-danger">Error loading reservations.</p>';
                });
        });

        // Delete User Modal
        const deleteUserModal = document.getElementById('deleteUserModal');
        deleteUserModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const userName = button.getAttribute('data-user-name');

            document.getElementById('delete_user_id').value = userId;
            document.getElementById('delete_user_name').textContent = userName;
        });

        // Format currency
        function formatCurrency(amount) {
            return '₱' + parseFloat(amount).toFixed(2);
        }

        // Format time
        function formatTime(time) {
            const [hours, minutes] = time.split(':');
            const hour = parseInt(hours);
            const ampm = hour >= 12 ? 'PM' : 'AM';
            const hour12 = hour % 12 || 12;
            return `${hour12}:${minutes} ${ampm}`;
        }
    </script>
</body>
</html>
