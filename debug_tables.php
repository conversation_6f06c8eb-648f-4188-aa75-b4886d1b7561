<?php
// Debug version of get_available_tables_client.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug: Available Tables</h1>\n";

// Test includes
echo "<h2>1. Testing Includes</h2>\n";
try {
    include 'config/config.php';
    echo "<p>✅ config.php included successfully</p>\n";
} catch (Exception $e) {
    echo "<p>❌ Error including config.php: " . $e->getMessage() . "</p>\n";
    exit;
}

try {
    include 'includes/functions.php';
    echo "<p>✅ functions.php included successfully</p>\n";
} catch (Exception $e) {
    echo "<p>❌ Error including functions.php: " . $e->getMessage() . "</p>\n";
    exit;
}

// Test database connection
echo "<h2>2. Testing Database Connection</h2>\n";
if (isset($conn) && $conn->ping()) {
    echo "<p>✅ Database connection successful</p>\n";
} else {
    echo "<p>❌ Database connection failed</p>\n";
    exit;
}

// Test parameters
echo "<h2>3. Testing Parameters</h2>\n";
$date = isset($_GET['date']) ? sanitize($_GET['date']) : '2024-12-13';
$time = isset($_GET['time']) ? sanitize($_GET['time']) : '15:00:00';
$guests = isset($_GET['guests']) ? (int)$_GET['guests'] : 2;

echo "<p>Date: $date</p>\n";
echo "<p>Time: $time</p>\n";
echo "<p>Guests: $guests</p>\n";

// Test functions
echo "<h2>4. Testing Functions</h2>\n";

// Test sanitize function
if (function_exists('sanitize')) {
    echo "<p>✅ sanitize() function exists</p>\n";
} else {
    echo "<p>❌ sanitize() function missing</p>\n";
}

// Test getAvailableTables function
if (function_exists('getAvailableTables')) {
    echo "<p>✅ getAvailableTables() function exists</p>\n";
    
    try {
        $available_tables = getAvailableTables($date, $time, $guests);
        echo "<p>✅ getAvailableTables() executed successfully</p>\n";
        echo "<p>Found " . count($available_tables) . " available tables</p>\n";
        
        if (!empty($available_tables)) {
            echo "<table border='1'><tr><th>Table ID</th><th>Capacity</th></tr>\n";
            foreach ($available_tables as $table) {
                echo "<tr><td>{$table['id']}</td><td>{$table['capacity']}</td></tr>\n";
            }
            echo "</table>\n";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error in getAvailableTables(): " . $e->getMessage() . "</p>\n";
    }
} else {
    echo "<p>❌ getAvailableTables() function missing</p>\n";
}

// Test findOptimalTables function
if (function_exists('findOptimalTables')) {
    echo "<p>✅ findOptimalTables() function exists</p>\n";
    
    if (!empty($available_tables)) {
        try {
            $optimal_table_ids = findOptimalTables($available_tables, $guests);
            echo "<p>✅ findOptimalTables() executed successfully</p>\n";
            echo "<p>Optimal table IDs: " . implode(', ', $optimal_table_ids) . "</p>\n";
        } catch (Exception $e) {
            echo "<p>❌ Error in findOptimalTables(): " . $e->getMessage() . "</p>\n";
        }
    }
} else {
    echo "<p>❌ findOptimalTables() function missing</p>\n";
}

// Test JSON output
echo "<h2>5. Testing JSON Output</h2>\n";
if (!empty($available_tables)) {
    $response = [
        'available_tables' => $available_tables,
        'optimal_tables' => $optimal_table_ids ?? [],
        'date' => $date,
        'time' => $time,
        'guests' => $guests
    ];
    
    echo "<p>JSON Response:</p>\n";
    echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>\n";
} else {
    echo "<p>No available tables to show in JSON</p>\n";
}

// Test actual AJAX endpoint
echo "<h2>6. Testing Actual AJAX Endpoint</h2>\n";
$ajax_url = "get_available_tables_client.php?date=$date&time=$time&guests=$guests";
echo "<p><a href='$ajax_url' target='_blank'>Test AJAX endpoint: $ajax_url</a></p>\n";

?>
