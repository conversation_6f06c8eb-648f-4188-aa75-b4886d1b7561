<?php
session_start();
header('Content-Type: application/json');
include 'config/config.php';
include 'includes/functions.php';

// Get parameters
$date = isset($_GET['date']) ? sanitize($_GET['date']) : '';
$time = isset($_GET['time']) ? sanitize($_GET['time']) : '';
$guests = isset($_GET['guests']) ? (int)$_GET['guests'] : 0;

// Validate parameters
if (empty($date) || empty($time) || $guests <= 0) {
    echo json_encode([
        'error' => 'Invalid parameters. Date, time, and number of guests are required.'
    ]);
    exit;
}

// Validate date format
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
    echo json_encode([
        'error' => 'Invalid date format. Please use YYYY-MM-DD format.'
    ]);
    exit;
}

// Validate time format
if (!preg_match('/^\d{2}:\d{2}:\d{2}$/', $time)) {
    echo json_encode([
        'error' => 'Invalid time format. Please use HH:MM:SS format.'
    ]);
    exit;
}

// Check if date is not in the past
$current_date = date('Y-m-d');
if ($date < $current_date) {
    echo json_encode([
        'error' => 'Cannot make reservations for past dates.'
    ]);
    exit;
}

// Check if time is within operating hours (9:00 AM - 9:00 PM)
$time_obj = new DateTime($time);
$open_time = new DateTime('09:00:00');
$close_time = new DateTime('21:00:00');

if ($time_obj < $open_time || $time_obj > $close_time) {
    echo json_encode([
        'error' => 'Reservation time must be between 9:00 AM and 9:00 PM.'
    ]);
    exit;
}

try {
    // Get available tables
    $available_tables = getAvailableTables($date, $time, $guests);

    if (empty($available_tables)) {
        echo json_encode([
            'error' => 'No tables are available for the selected date, time, and party size.',
            'available_tables' => [],
            'optimal_tables' => [],
            'date' => $date,
            'time' => $time,
            'guests' => $guests
        ]);
        exit;
    }

    // Find optimal tables (tables that best fit the party size)
    $optimal_table_ids = findOptimalTables($available_tables, $guests);

    // Return successful response
    echo json_encode([
        'available_tables' => $available_tables,
        'optimal_tables' => $optimal_table_ids,
        'date' => $date,
        'time' => $time,
        'guests' => $guests
    ]);

} catch (Exception $e) {
    echo json_encode([
        'error' => 'An error occurred while fetching available tables: ' . $e->getMessage()
    ]);
}
?>
