<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX Table Loading</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test AJAX Table Loading</h1>
    
    <div id="test-results"></div>
    
    <script>
        $(document).ready(function() {
            const testUrl = 'get_available_tables_client.php';
            const testParams = {
                date: '2024-12-13',
                time: '15:00:00',
                guests: 2
            };
            
            $('#test-results').html('<p>Testing AJAX call...</p>');
            
            $.ajax({
                url: testUrl,
                type: 'GET',
                data: testParams,
                dataType: 'json',
                success: function(response) {
                    console.log('AJAX Success:', response);
                    
                    let html = '<h2>✅ AJAX Success</h2>';
                    html += '<pre>' + JSON.stringify(response, null, 2) + '</pre>';
                    
                    if (response.available_tables && response.available_tables.length > 0) {
                        html += '<h3>Available Tables:</h3>';
                        html += '<table border="1"><tr><th>Table ID</th><th>Capacity</th></tr>';
                        response.available_tables.forEach(function(table) {
                            html += '<tr><td>' + table.id + '</td><td>' + table.capacity + '</td></tr>';
                        });
                        html += '</table>';
                    }
                    
                    $('#test-results').html(html);
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', xhr, status, error);
                    
                    let html = '<h2>❌ AJAX Error</h2>';
                    html += '<p><strong>Status:</strong> ' + status + '</p>';
                    html += '<p><strong>Error:</strong> ' + error + '</p>';
                    html += '<p><strong>Response Text:</strong></p>';
                    html += '<pre>' + xhr.responseText + '</pre>';
                    
                    $('#test-results').html(html);
                }
            });
        });
    </script>
</body>
</html>
