<?php
echo "PHP is working!<br>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";
echo "Server: " . $_SERVER['SERVER_NAME'] . "<br>";

// Test database connection
try {
    $conn = new mysqli('localhost', 'root', '', 'raymart_diner');
    if ($conn->connect_error) {
        echo "Database connection failed: " . $conn->connect_error . "<br>";
    } else {
        echo "Database connection successful!<br>";
        
        // Test tables count
        $result = $conn->query("SELECT COUNT(*) as count FROM tables");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "Tables in database: " . $row['count'] . "<br>";
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}
?>
