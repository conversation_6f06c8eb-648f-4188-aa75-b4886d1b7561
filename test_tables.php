<?php
// Test script to check table availability functionality
include 'config/config.php';
include 'includes/functions.php';

echo "<h1>Table Availability Test</h1>\n";

// Test 1: Check if tables exist
echo "<h2>1. Tables in Database</h2>\n";
$result = $conn->query("SELECT * FROM tables ORDER BY id");
if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>Capacity</th><th>Is Reserved</th></tr>\n";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['capacity']}</td>";
        echo "<td>" . ($row['is_reserved'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>❌ No tables found in database!</p>\n";
}

// Test 2: Test getAvailableTables function
echo "<h2>2. Available Tables Function Test</h2>\n";
$test_date = '2024-12-13';
$test_time = '15:00:00';
$test_guests = 2;

echo "<p>Testing for: Date=$test_date, Time=$test_time, Guests=$test_guests</p>\n";

try {
    $available_tables = getAvailableTables($test_date, $test_time, $test_guests);
    
    if (!empty($available_tables)) {
        echo "<p>✅ Found " . count($available_tables) . " available tables:</p>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>Table ID</th><th>Capacity</th></tr>\n";
        foreach ($available_tables as $table) {
            echo "<tr>";
            echo "<td>{$table['id']}</td>";
            echo "<td>{$table['capacity']}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p>❌ No available tables found!</p>\n";
    }
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>\n";
}

// Test 3: Test findOptimalTables function
echo "<h2>3. Optimal Tables Function Test</h2>\n";
if (!empty($available_tables)) {
    try {
        $optimal_table_ids = findOptimalTables($available_tables, $test_guests);
        echo "<p>✅ Optimal table IDs: " . implode(', ', $optimal_table_ids) . "</p>\n";
    } catch (Exception $e) {
        echo "<p>❌ Error in findOptimalTables: " . $e->getMessage() . "</p>\n";
    }
} else {
    echo "<p>⚠️ Cannot test optimal tables - no available tables</p>\n";
}

// Test 4: Check existing reservations
echo "<h2>4. Existing Reservations</h2>\n";
$result = $conn->query("SELECT r.*, rt.table_id FROM reservations r LEFT JOIN reservation_tables rt ON r.id = rt.reservation_id WHERE r.date = '$test_date' ORDER BY r.time");
if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>Date</th><th>Time</th><th>Guests</th><th>Status</th><th>Table ID</th></tr>\n";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['date']}</td>";
        echo "<td>{$row['time']}</td>";
        echo "<td>{$row['guests']}</td>";
        echo "<td>{$row['status']}</td>";
        echo "<td>" . ($row['table_id'] ?? 'None') . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>✅ No existing reservations for $test_date</p>\n";
}

// Test 5: Test the AJAX endpoint
echo "<h2>5. AJAX Endpoint Test</h2>\n";
$url = "http://localhost/restaurant-management-system3/get_available_tables_client.php?date=$test_date&time=$test_time&guests=$test_guests";
echo "<p>Testing URL: <a href='$url' target='_blank'>$url</a></p>\n";

// Test 6: Database connection info
echo "<h2>6. Database Connection Info</h2>\n";
echo "<p>Database: " . DB_NAME . "</p>\n";
echo "<p>Connection status: " . ($conn->ping() ? "✅ Connected" : "❌ Disconnected") . "</p>\n";

?>
